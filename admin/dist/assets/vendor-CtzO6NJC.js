/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function e(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}const t={},n=[],r=()=>{},o=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),a=Object.assign,i=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},c=Object.prototype.hasOwnProperty,u=(e,t)=>c.call(e,t),f=Array.isArray,p=e=>"[object Map]"===k(e),d=e=>"[object Set]"===k(e),h=e=>"[object Date]"===k(e),m=e=>"function"==typeof e,g=e=>"string"==typeof e,v=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,y=e=>(_(e)||m(e))&&m(e.then)&&m(e.catch),b=Object.prototype.toString,k=e=>b.call(e),w=e=>"[object Object]"===k(e),x=e=>g(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,S=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),C=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},L=/-(\w)/g,O=C(e=>e.replace(L,(e,t)=>t?t.toUpperCase():"")),E=/\B([A-Z])/g,F=C(e=>e.replace(E,"-$1").toLowerCase()),T=C(e=>e.charAt(0).toUpperCase()+e.slice(1)),P=C(e=>e?`on${T(e)}`:""),I=(e,t)=>!Object.is(e,t),$=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},R=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},M=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let A;const j=()=>A||(A="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function N(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=g(r)?U(r):N(r);if(o)for(const e in o)t[e]=o[e]}return t}if(g(e)||_(e))return e}const W=/;(?![^(]*\))/g,D=/:([^]+)/,V=/\/\*[^]*?\*\//g;function U(e){const t={};return e.replace(V,"").split(W).forEach(e=>{if(e){const n=e.split(D);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function H(e){let t="";if(g(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const r=H(e[n]);r&&(t+=r+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const B=e("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function G(e){return!!e||""===e}function z(e,t){if(e===t)return!0;let n=h(e),r=h(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=v(e),r=v(t),n||r)return e===t;if(n=f(e),r=f(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=z(e[r],t[r]);return n}(e,t);if(n=_(e),r=_(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!z(e[n],t[n]))return!1}}return String(e)===String(t)}function Y(e,t){return e.findIndex(e=>z(e,t))}const q=e=>!(!e||!0!==e.__v_isRef),K=e=>g(e)?e:null==e?"":f(e)||_(e)&&(e.toString===b||!m(e.toString))?q(e)?K(e.value):JSON.stringify(e,J,2):String(e),J=(e,t)=>q(t)?J(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],r)=>(e[Z(t,r)+" =>"]=n,e),{})}:d(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>Z(e))}:v(t)?Z(t):!_(t)||f(t)||w(t)?t:String(t),Z=(e,t="")=>{var n;return v(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};
/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let X,Q;class ee{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=X,!e&&X&&(this.index=(X.scopes||(X.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=X;try{return X=this,e()}finally{X=t}}}on(){1===++this._on&&(this.prevScope=X,X=this)}off(){this._on>0&&0===--this._on&&(X=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){let t,n;for(this._active=!1,t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}}function te(e){return new ee(e)}function ne(){return X}const re=new WeakSet;class oe{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,X&&X.active&&X.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,re.has(this)&&(re.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||ie(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,ke(this),fe(this);const e=Q,t=ve;Q=this,ve=!0;try{return this.fn()}finally{pe(this),Q=e,ve=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)me(e);this.deps=this.depsTail=void 0,ke(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?re.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){de(this)&&this.run()}get dirty(){return de(this)}}let se,le,ae=0;function ie(e,t=!1){if(e.flags|=8,t)return e.next=le,void(le=e);e.next=se,se=e}function ce(){ae++}function ue(){if(--ae>0)return;if(le){let e=le;for(le=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;se;){let n=se;for(se=void 0;n;){const r=n.next;if(n.next=void 0,n.flags&=-9,1&n.flags)try{n.trigger()}catch(t){e||(e=t)}n=r}}if(e)throw e}function fe(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function pe(e){let t,n=e.depsTail,r=n;for(;r;){const e=r.prevDep;-1===r.version?(r===n&&(n=e),me(r),ge(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=e}e.deps=t,e.depsTail=n}function de(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(he(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function he(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===we)return;if(e.globalVersion=we,!e.isSSR&&128&e.flags&&(!e.deps&&!e._dirty||!de(e)))return;e.flags|=2;const t=e.dep,n=Q,r=ve;Q=e,ve=!0;try{fe(e);const n=e.fn(e._value);(0===t.version||I(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(o){throw t.version++,o}finally{Q=n,ve=r,pe(e),e.flags&=-3}}function me(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)me(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function ge(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let ve=!0;const _e=[];function ye(){_e.push(ve),ve=!1}function be(){const e=_e.pop();ve=void 0===e||e}function ke(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=Q;Q=void 0;try{t()}finally{Q=e}}}let we=0;class xe{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Se{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!Q||!ve||Q===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==Q)t=this.activeLink=new xe(Q,this),Q.deps?(t.prevDep=Q.depsTail,Q.depsTail.nextDep=t,Q.depsTail=t):Q.deps=Q.depsTail=t,Ce(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=Q.depsTail,t.nextDep=void 0,Q.depsTail.nextDep=t,Q.depsTail=t,Q.deps===t&&(Q.deps=e)}return t}trigger(e){this.version++,we++,this.notify(e)}notify(e){ce();try{0;for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{ue()}}}function Ce(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)Ce(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Le=new WeakMap,Oe=Symbol(""),Ee=Symbol(""),Fe=Symbol("");function Te(e,t,n){if(ve&&Q){let t=Le.get(e);t||Le.set(e,t=new Map);let r=t.get(n);r||(t.set(n,r=new Se),r.map=t,r.key=n),r.track()}}function Pe(e,t,n,r,o,s){const l=Le.get(e);if(!l)return void we++;const a=e=>{e&&e.trigger()};if(ce(),"clear"===t)l.forEach(a);else{const o=f(e),s=o&&x(n);if(o&&"length"===n){const e=Number(r);l.forEach((t,n)=>{("length"===n||n===Fe||!v(n)&&n>=e)&&a(t)})}else switch((void 0!==n||l.has(void 0))&&a(l.get(n)),s&&a(l.get(Fe)),t){case"add":o?s&&a(l.get("length")):(a(l.get(Oe)),p(e)&&a(l.get(Ee)));break;case"delete":o||(a(l.get(Oe)),p(e)&&a(l.get(Ee)));break;case"set":p(e)&&a(l.get(Oe))}}ue()}function Ie(e){const t=vt(e);return t===e?t:(Te(t,0,Fe),mt(e)?t:t.map(yt))}function $e(e){return Te(e=vt(e),0,Fe),e}const Re={__proto__:null,[Symbol.iterator](){return Me(this,Symbol.iterator,yt)},concat(...e){return Ie(this).concat(...e.map(e=>f(e)?Ie(e):e))},entries(){return Me(this,"entries",e=>(e[1]=yt(e[1]),e))},every(e,t){return je(this,"every",e,t,void 0,arguments)},filter(e,t){return je(this,"filter",e,t,e=>e.map(yt),arguments)},find(e,t){return je(this,"find",e,t,yt,arguments)},findIndex(e,t){return je(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return je(this,"findLast",e,t,yt,arguments)},findLastIndex(e,t){return je(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return je(this,"forEach",e,t,void 0,arguments)},includes(...e){return We(this,"includes",e)},indexOf(...e){return We(this,"indexOf",e)},join(e){return Ie(this).join(e)},lastIndexOf(...e){return We(this,"lastIndexOf",e)},map(e,t){return je(this,"map",e,t,void 0,arguments)},pop(){return De(this,"pop")},push(...e){return De(this,"push",e)},reduce(e,...t){return Ne(this,"reduce",e,t)},reduceRight(e,...t){return Ne(this,"reduceRight",e,t)},shift(){return De(this,"shift")},some(e,t){return je(this,"some",e,t,void 0,arguments)},splice(...e){return De(this,"splice",e)},toReversed(){return Ie(this).toReversed()},toSorted(e){return Ie(this).toSorted(e)},toSpliced(...e){return Ie(this).toSpliced(...e)},unshift(...e){return De(this,"unshift",e)},values(){return Me(this,"values",yt)}};function Me(e,t,n){const r=$e(e),o=r[t]();return r===e||mt(e)||(o._next=o.next,o.next=()=>{const e=o._next();return e.value&&(e.value=n(e.value)),e}),o}const Ae=Array.prototype;function je(e,t,n,r,o,s){const l=$e(e),a=l!==e&&!mt(e),i=l[t];if(i!==Ae[t]){const t=i.apply(e,s);return a?yt(t):t}let c=n;l!==e&&(a?c=function(t,r){return n.call(this,yt(t),r,e)}:n.length>2&&(c=function(t,r){return n.call(this,t,r,e)}));const u=i.call(l,c,r);return a&&o?o(u):u}function Ne(e,t,n,r){const o=$e(e);let s=n;return o!==e&&(mt(e)?n.length>3&&(s=function(t,r,o){return n.call(this,t,r,o,e)}):s=function(t,r,o){return n.call(this,t,yt(r),o,e)}),o[t](s,...r)}function We(e,t,n){const r=vt(e);Te(r,0,Fe);const o=r[t](...n);return-1!==o&&!1!==o||!gt(n[0])?o:(n[0]=vt(n[0]),r[t](...n))}function De(e,t,n=[]){ye(),ce();const r=vt(e)[t].apply(e,n);return ue(),be(),r}const Ve=e("__proto__,__v_isRef,__isVue"),Ue=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(v));function He(e){v(e)||(e=String(e));const t=vt(this);return Te(t,0,e),t.hasOwnProperty(e)}class Be{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if("__v_skip"===t)return e.__v_skip;const r=this._isReadonly,o=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return o;if("__v_raw"===t)return n===(r?o?at:lt:o?st:ot).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const s=f(e);if(!r){let e;if(s&&(e=Re[t]))return e;if("hasOwnProperty"===t)return He}const l=Reflect.get(e,t,kt(e)?e:n);return(v(t)?Ue.has(t):Ve(t))?l:(r||Te(e,0,t),o?l:kt(l)?s&&x(t)?l:l.value:_(l)?r?ft(l):ct(l):l)}}class Ge extends Be{constructor(e=!1){super(!1,e)}set(e,t,n,r){let o=e[t];if(!this._isShallow){const t=ht(o);if(mt(n)||ht(n)||(o=vt(o),n=vt(n)),!f(e)&&kt(o)&&!kt(n))return!t&&(o.value=n,!0)}const s=f(e)&&x(t)?Number(t)<e.length:u(e,t),l=Reflect.set(e,t,n,kt(e)?e:r);return e===vt(r)&&(s?I(n,o)&&Pe(e,"set",t,n):Pe(e,"add",t,n)),l}deleteProperty(e,t){const n=u(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&Pe(e,"delete",t,void 0),r}has(e,t){const n=Reflect.has(e,t);return v(t)&&Ue.has(t)||Te(e,0,t),n}ownKeys(e){return Te(e,0,f(e)?"length":Oe),Reflect.ownKeys(e)}}class ze extends Be{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Ye=new Ge,qe=new ze,Ke=new Ge(!0),Je=e=>e,Ze=e=>Reflect.getPrototypeOf(e);function Xe(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Qe(e,t){const n={get(n){const r=this.__v_raw,o=vt(r),s=vt(n);e||(I(n,s)&&Te(o,0,n),Te(o,0,s));const{has:l}=Ze(o),a=t?Je:e?bt:yt;return l.call(o,n)?a(r.get(n)):l.call(o,s)?a(r.get(s)):void(r!==o&&r.get(n))},get size(){const t=this.__v_raw;return!e&&Te(vt(t),0,Oe),Reflect.get(t,"size",t)},has(t){const n=this.__v_raw,r=vt(n),o=vt(t);return e||(I(t,o)&&Te(r,0,t),Te(r,0,o)),t===o?n.has(t):n.has(t)||n.has(o)},forEach(n,r){const o=this,s=o.__v_raw,l=vt(s),a=t?Je:e?bt:yt;return!e&&Te(l,0,Oe),s.forEach((e,t)=>n.call(r,a(e),a(t),o))}};a(n,e?{add:Xe("add"),set:Xe("set"),delete:Xe("delete"),clear:Xe("clear")}:{add(e){t||mt(e)||ht(e)||(e=vt(e));const n=vt(this);return Ze(n).has.call(n,e)||(n.add(e),Pe(n,"add",e,e)),this},set(e,n){t||mt(n)||ht(n)||(n=vt(n));const r=vt(this),{has:o,get:s}=Ze(r);let l=o.call(r,e);l||(e=vt(e),l=o.call(r,e));const a=s.call(r,e);return r.set(e,n),l?I(n,a)&&Pe(r,"set",e,n):Pe(r,"add",e,n),this},delete(e){const t=vt(this),{has:n,get:r}=Ze(t);let o=n.call(t,e);o||(e=vt(e),o=n.call(t,e)),r&&r.call(t,e);const s=t.delete(e);return o&&Pe(t,"delete",e,void 0),s},clear(){const e=vt(this),t=0!==e.size,n=e.clear();return t&&Pe(e,"clear",void 0,void 0),n}});return["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=function(e,t,n){return function(...r){const o=this.__v_raw,s=vt(o),l=p(s),a="entries"===e||e===Symbol.iterator&&l,i="keys"===e&&l,c=o[e](...r),u=n?Je:t?bt:yt;return!t&&Te(s,0,i?Ee:Oe),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}(r,e,t)}),n}function et(e,t){const n=Qe(e,t);return(t,r,o)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(u(n,r)&&r in t?n:t,r,o)}const tt={get:et(!1,!1)},nt={get:et(!1,!0)},rt={get:et(!0,!1)},ot=new WeakMap,st=new WeakMap,lt=new WeakMap,at=new WeakMap;function it(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>k(e).slice(8,-1))(e))}function ct(e){return ht(e)?e:pt(e,!1,Ye,tt,ot)}function ut(e){return pt(e,!1,Ke,nt,st)}function ft(e){return pt(e,!0,qe,rt,lt)}function pt(e,t,n,r,o){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=it(e);if(0===s)return e;const l=o.get(e);if(l)return l;const a=new Proxy(e,2===s?r:n);return o.set(e,a),a}function dt(e){return ht(e)?dt(e.__v_raw):!(!e||!e.__v_isReactive)}function ht(e){return!(!e||!e.__v_isReadonly)}function mt(e){return!(!e||!e.__v_isShallow)}function gt(e){return!!e&&!!e.__v_raw}function vt(e){const t=e&&e.__v_raw;return t?vt(t):e}function _t(e){return!u(e,"__v_skip")&&Object.isExtensible(e)&&R(e,"__v_skip",!0),e}const yt=e=>_(e)?ct(e):e,bt=e=>_(e)?ft(e):e;function kt(e){return!!e&&!0===e.__v_isRef}function wt(e){return St(e,!1)}function xt(e){return St(e,!0)}function St(e,t){return kt(e)?e:new Ct(e,t)}class Ct{constructor(e,t){this.dep=new Se,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:vt(e),this._value=t?e:yt(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||mt(e)||ht(e);e=n?e:vt(e),I(e,t)&&(this._rawValue=e,this._value=n?e:yt(e),this.dep.trigger())}}function Lt(e){return kt(e)?e.value:e}const Ot={get:(e,t,n)=>"__v_raw"===t?e:Lt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return kt(o)&&!kt(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Et(e){return dt(e)?e:new Proxy(e,Ot)}class Ft{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){const n=Le.get(e);return n&&n.get(t)}(vt(this._object),this._key)}}class Tt{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Pt(e,t,n){return kt(e)?e:m(e)?new Tt(e):_(e)&&arguments.length>1?It(e,t,n):wt(e)}function It(e,t,n){const r=e[t];return kt(r)?r:new Ft(e,t,n)}class $t{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Se(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=we-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags)&&Q!==this)return ie(this,!0),!0}get value(){const e=this.dep.track();return he(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}const Rt={},Mt=new WeakMap;let At;function jt(e,n,o=t){const{immediate:s,deep:l,once:a,scheduler:c,augmentJob:u,call:p}=o,d=e=>l?e:mt(e)||!1===l||0===l?Nt(e,1):Nt(e);let h,g,v,_,y=!1,b=!1;if(kt(e)?(g=()=>e.value,y=mt(e)):dt(e)?(g=()=>d(e),y=!0):f(e)?(b=!0,y=e.some(e=>dt(e)||mt(e)),g=()=>e.map(e=>kt(e)?e.value:dt(e)?d(e):m(e)?p?p(e,2):e():void 0)):g=m(e)?n?p?()=>p(e,2):e:()=>{if(v){ye();try{v()}finally{be()}}const t=At;At=h;try{return p?p(e,3,[_]):e(_)}finally{At=t}}:r,n&&l){const e=g,t=!0===l?1/0:l;g=()=>Nt(e(),t)}const k=ne(),w=()=>{h.stop(),k&&k.active&&i(k.effects,h)};if(a&&n){const e=n;n=(...t)=>{e(...t),w()}}let x=b?new Array(e.length).fill(Rt):Rt;const S=e=>{if(1&h.flags&&(h.dirty||e))if(n){const e=h.run();if(l||y||(b?e.some((e,t)=>I(e,x[t])):I(e,x))){v&&v();const t=At;At=h;try{const t=[e,x===Rt?void 0:b&&x[0]===Rt?[]:x,_];x=e,p?p(n,3,t):n(...t)}finally{At=t}}}else h.run()};return u&&u(S),h=new oe(g),h.scheduler=c?()=>c(S,!1):S,_=e=>function(e,t=!1,n=At){if(n){let t=Mt.get(n);t||Mt.set(n,t=[]),t.push(e)}}(e,!1,h),v=h.onStop=()=>{const e=Mt.get(h);if(e){if(p)p(e,4);else for(const t of e)t();Mt.delete(h)}},n?s?S(!0):x=h.run():c?c(S.bind(null,!0),!0):h.run(),w.pause=h.pause.bind(h),w.resume=h.resume.bind(h),w.stop=w,w}function Nt(e,t=1/0,n){if(t<=0||!_(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,kt(e))Nt(e.value,t,n);else if(f(e))for(let r=0;r<e.length;r++)Nt(e[r],t,n);else if(d(e)||p(e))e.forEach(e=>{Nt(e,t,n)});else if(w(e)){for(const r in e)Nt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Nt(e[r],t,n)}return e}
/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Wt(e,t,n,r){try{return r?e(...r):e()}catch(o){Vt(o,t,n)}}function Dt(e,t,n,r){if(m(e)){const o=Wt(e,t,n,r);return o&&y(o)&&o.catch(e=>{Vt(e,t,n)}),o}if(f(e)){const o=[];for(let s=0;s<e.length;s++)o.push(Dt(e[s],t,n,r));return o}}function Vt(e,n,r,o=!0){n&&n.vnode;const{errorHandler:s,throwUnhandledErrorInProduction:l}=n&&n.appContext.config||t;if(n){let t=n.parent;const o=n.proxy,l=`https://vuejs.org/error-reference/#runtime-${r}`;for(;t;){const n=t.ec;if(n)for(let t=0;t<n.length;t++)if(!1===n[t](e,o,l))return;t=t.parent}if(s)return ye(),Wt(s,null,10,[e,o,l]),void be()}!function(e,t,n,r=!0,o=!1){if(o)throw e}(e,0,0,o,l)}const Ut=[];let Ht=-1;const Bt=[];let Gt=null,zt=0;const Yt=Promise.resolve();let qt=null;function Kt(e){const t=qt||Yt;return e?t.then(this?e.bind(this):e):t}function Jt(e){if(!(1&e.flags)){const t=en(e),n=Ut[Ut.length-1];!n||!(2&e.flags)&&t>=en(n)?Ut.push(e):Ut.splice(function(e){let t=Ht+1,n=Ut.length;for(;t<n;){const r=t+n>>>1,o=Ut[r],s=en(o);s<e||s===e&&2&o.flags?t=r+1:n=r}return t}(t),0,e),e.flags|=1,Zt()}}function Zt(){qt||(qt=Yt.then(tn))}function Xt(e,t,n=Ht+1){for(;n<Ut.length;n++){const t=Ut[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;Ut.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function Qt(e){if(Bt.length){const e=[...new Set(Bt)].sort((e,t)=>en(e)-en(t));if(Bt.length=0,Gt)return void Gt.push(...e);for(Gt=e,zt=0;zt<Gt.length;zt++){const e=Gt[zt];4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}Gt=null,zt=0}}const en=e=>null==e.id?2&e.flags?-1:1/0:e.id;function tn(e){try{for(Ht=0;Ht<Ut.length;Ht++){const e=Ut[Ht];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),Wt(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;Ht<Ut.length;Ht++){const e=Ut[Ht];e&&(e.flags&=-2)}Ht=-1,Ut.length=0,Qt(),qt=null,(Ut.length||Bt.length)&&tn()}}let nn=null,rn=null;function on(e){const t=nn;return nn=e,rn=e&&e.type.__scopeId||null,t}function sn(e,t=nn,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&ho(-1);const o=on(t);let s;try{s=e(...n)}finally{on(o),r._d&&ho(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}function ln(e,n){if(null===nn)return e;const r=Go(nn),o=e.dirs||(e.dirs=[]);for(let s=0;s<n.length;s++){let[e,l,a,i=t]=n[s];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&Nt(l),o.push({dir:e,instance:r,value:l,oldValue:void 0,arg:a,modifiers:i}))}return e}function an(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let l=0;l<o.length;l++){const a=o[l];s&&(a.oldValue=s[l].value);let i=a.dir[r];i&&(ye(),Dt(i,n,8,[e.el,a,e,t]),be())}}const cn=Symbol("_vte"),un=e=>e.__isTeleport,fn=Symbol("_leaveCb"),pn=Symbol("_enterCb");const dn=[Function,Array],hn={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:dn,onEnter:dn,onAfterEnter:dn,onEnterCancelled:dn,onBeforeLeave:dn,onLeave:dn,onAfterLeave:dn,onLeaveCancelled:dn,onBeforeAppear:dn,onAppear:dn,onAfterAppear:dn,onAppearCancelled:dn},mn=e=>{const t=e.subTree;return t.component?mn(t.component):t};function gn(e){let t=e[0];if(e.length>1)for(const n of e)if(n.type!==ao){t=n;break}return t}const vn={name:"BaseTransition",props:hn,setup(e,{slots:t}){const n=Mo(),r=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return An(()=>{e.isMounted=!0}),Wn(()=>{e.isUnmounting=!0}),e}();return()=>{const o=t.default&&xn(t.default(),!0);if(!o||!o.length)return;const s=gn(o),l=vt(e),{mode:a}=l;if(r.isLeaving)return bn(s);const i=kn(s);if(!i)return bn(s);let c=yn(i,l,r,n,e=>c=e);i.type!==ao&&wn(i,c);let u=n.subTree&&kn(n.subTree);if(u&&u.type!==ao&&!yo(i,u)&&mn(n).type!==ao){let e=yn(u,l,r,n);if(wn(u,e),"out-in"===a&&i.type!==ao)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave,u=void 0},bn(s);"in-out"===a&&i.type!==ao?e.delayLeave=(e,t,n)=>{_n(r,u)[String(u.key)]=u,e[fn]=()=>{t(),e[fn]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{n(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return s}}};function _n(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function yn(e,t,n,r,o){const{appear:s,mode:l,persisted:a=!1,onBeforeEnter:i,onEnter:c,onAfterEnter:u,onEnterCancelled:p,onBeforeLeave:d,onLeave:h,onAfterLeave:m,onLeaveCancelled:g,onBeforeAppear:v,onAppear:_,onAfterAppear:y,onAppearCancelled:b}=t,k=String(e.key),w=_n(n,e),x=(e,t)=>{e&&Dt(e,r,9,t)},S=(e,t)=>{const n=t[1];x(e,t),f(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},C={mode:l,persisted:a,beforeEnter(t){let r=i;if(!n.isMounted){if(!s)return;r=v||i}t[fn]&&t[fn](!0);const o=w[k];o&&yo(e,o)&&o.el[fn]&&o.el[fn](),x(r,[t])},enter(e){let t=c,r=u,o=p;if(!n.isMounted){if(!s)return;t=_||c,r=y||u,o=b||p}let l=!1;const a=e[pn]=t=>{l||(l=!0,x(t?o:r,[e]),C.delayedLeave&&C.delayedLeave(),e[pn]=void 0)};t?S(t,[e,a]):a()},leave(t,r){const o=String(e.key);if(t[pn]&&t[pn](!0),n.isUnmounting)return r();x(d,[t]);let s=!1;const l=t[fn]=n=>{s||(s=!0,r(),x(n?g:m,[t]),t[fn]=void 0,w[o]===e&&delete w[o])};w[o]=e,h?S(h,[t,l]):l()},clone(e){const s=yn(e,t,n,r,o);return o&&o(s),s}};return C}function bn(e){if(En(e))return(e=So(e)).children=null,e}function kn(e){if(!En(e))return un(e.type)&&e.children?gn(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&m(n.default))return n.default()}}function wn(e,t){6&e.shapeFlag&&e.component?(e.transition=t,wn(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function xn(e,t=!1,n){let r=[],o=0;for(let s=0;s<e.length;s++){let l=e[s];const a=null==n?l.key:String(n)+String(null!=l.key?l.key:s);l.type===so?(128&l.patchFlag&&o++,r=r.concat(xn(l.children,t,a))):(t||l.type!==ao)&&r.push(null!=a?So(l,{key:a}):l)}if(o>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}
/*! #__NO_SIDE_EFFECTS__ */function Sn(e,t){return m(e)?(()=>a({name:e.name},t,{setup:e}))():e}function Cn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Ln(e,n,r,o,s=!1){if(f(e))return void e.forEach((e,t)=>Ln(e,n&&(f(n)?n[t]:n),r,o,s));if(On(o)&&!s)return void(512&o.shapeFlag&&o.type.__asyncResolved&&o.component.subTree.component&&Ln(e,n,r,o.component.subTree));const l=4&o.shapeFlag?Go(o.component):o.el,a=s?null:l,{i:c,r:p}=e,d=n&&n.r,h=c.refs===t?c.refs={}:c.refs,v=c.setupState,_=vt(v),y=v===t?()=>!1:e=>u(_,e);if(null!=d&&d!==p&&(g(d)?(h[d]=null,y(d)&&(v[d]=null)):kt(d)&&(d.value=null)),m(p))Wt(p,c,12,[a,h]);else{const t=g(p),n=kt(p);if(t||n){const o=()=>{if(e.f){const n=t?y(p)?v[p]:h[p]:p.value;s?f(n)&&i(n,l):f(n)?n.includes(l)||n.push(l):t?(h[p]=[l],y(p)&&(v[p]=h[p])):(p.value=[l],e.k&&(h[e.k]=p.value))}else t?(h[p]=a,y(p)&&(v[p]=a)):n&&(p.value=a,e.k&&(h[e.k]=a))};a?(o.id=-1,jr(o,r)):o()}}}j().requestIdleCallback,j().cancelIdleCallback;const On=e=>!!e.type.__asyncLoader,En=e=>e.type.__isKeepAlive;function Fn(e,t){Pn(e,"a",t)}function Tn(e,t){Pn(e,"da",t)}function Pn(e,t,n=Ro){const r=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if($n(t,r,n),n){let e=n.parent;for(;e&&e.parent;)En(e.parent.vnode)&&In(r,t,n,e),e=e.parent}}function In(e,t,n,r){const o=$n(t,e,r,!0);Dn(()=>{i(r[t],o)},n)}function $n(e,t,n=Ro,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...r)=>{ye();const o=No(n),s=Dt(t,n,e,r);return o(),be(),s});return r?o.unshift(s):o.push(s),s}}const Rn=e=>(t,n=Ro)=>{Vo&&"sp"!==e||$n(e,(...e)=>t(...e),n)},Mn=Rn("bm"),An=Rn("m"),jn=Rn("bu"),Nn=Rn("u"),Wn=Rn("bum"),Dn=Rn("um"),Vn=Rn("sp"),Un=Rn("rtg"),Hn=Rn("rtc");function Bn(e,t=Ro){$n("ec",e,t)}const Gn="components";function zn(e,t){return Kn(Gn,e,!0,t)||e}const Yn=Symbol.for("v-ndc");function qn(e){return g(e)?Kn(Gn,e,!1)||e:e||Yn}function Kn(e,t,n=!0,r=!1){const o=nn||Ro;if(o){const n=o.type;{const e=zo(n,!1);if(e&&(e===t||e===O(t)||e===T(O(t))))return n}const s=Jn(o[e]||n[e],t)||Jn(o.appContext[e],t);return!s&&r?n:s}}function Jn(e,t){return e&&(e[t]||e[O(t)]||e[T(O(t))])}function Zn(e,t,n,r){let o;const s=n,l=f(e);if(l||g(e)){let n=!1,r=!1;l&&dt(e)&&(n=!mt(e),r=ht(e),e=$e(e)),o=new Array(e.length);for(let l=0,a=e.length;l<a;l++)o[l]=t(n?r?bt(yt(e[l])):yt(e[l]):e[l],l,void 0,s)}else if("number"==typeof e){o=new Array(e);for(let n=0;n<e;n++)o[n]=t(n+1,n,void 0,s)}else if(_(e))if(e[Symbol.iterator])o=Array.from(e,(e,n)=>t(e,n,void 0,s));else{const n=Object.keys(e);o=new Array(n.length);for(let r=0,l=n.length;r<l;r++){const l=n[r];o[r]=t(e[l],l,r,s)}}else o=[];return o}function Xn(e,t,n={},r,o){if(nn.ce||nn.parent&&On(nn.parent)&&nn.parent.ce)return fo(),vo(so,null,[xo("slot",n,r)],64);let s=e[t];s&&s._c&&(s._d=!1),fo();const l=s&&Qn(s(n)),a=n.key||l&&l.key,i=vo(so,{key:(a&&!v(a)?a:`_${t}`)+""},l||[],l&&1===e._?64:-2);return s&&s._c&&(s._d=!0),i}function Qn(e){return e.some(e=>!_o(e)||e.type!==ao&&!(e.type===so&&!Qn(e.children)))?e:null}const er=e=>e?Do(e)?Go(e):er(e.parent):null,tr=a(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>er(e.parent),$root:e=>er(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>cr(e),$forceUpdate:e=>e.f||(e.f=()=>{Jt(e.update)}),$nextTick:e=>e.n||(e.n=Kt.bind(e.proxy)),$watch:e=>qr.bind(e)}),nr=(e,n)=>e!==t&&!e.__isScriptSetup&&u(e,n),rr={get({_:e},n){if("__v_skip"===n)return!0;const{ctx:r,setupState:o,data:s,props:l,accessCache:a,type:i,appContext:c}=e;let f;if("$"!==n[0]){const i=a[n];if(void 0!==i)switch(i){case 1:return o[n];case 2:return s[n];case 4:return r[n];case 3:return l[n]}else{if(nr(o,n))return a[n]=1,o[n];if(s!==t&&u(s,n))return a[n]=2,s[n];if((f=e.propsOptions[0])&&u(f,n))return a[n]=3,l[n];if(r!==t&&u(r,n))return a[n]=4,r[n];sr&&(a[n]=0)}}const p=tr[n];let d,h;return p?("$attrs"===n&&Te(e.attrs,0,""),p(e)):(d=i.__cssModules)&&(d=d[n])?d:r!==t&&u(r,n)?(a[n]=4,r[n]):(h=c.config.globalProperties,u(h,n)?h[n]:void 0)},set({_:e},n,r){const{data:o,setupState:s,ctx:l}=e;return nr(s,n)?(s[n]=r,!0):o!==t&&u(o,n)?(o[n]=r,!0):!u(e.props,n)&&(("$"!==n[0]||!(n.slice(1)in e))&&(l[n]=r,!0))},has({_:{data:e,setupState:n,accessCache:r,ctx:o,appContext:s,propsOptions:l}},a){let i;return!!r[a]||e!==t&&u(e,a)||nr(n,a)||(i=l[0])&&u(i,a)||u(o,a)||u(tr,a)||u(s.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:u(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function or(e){return f(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}let sr=!0;function lr(e){const t=cr(e),n=e.proxy,o=e.ctx;sr=!1,t.beforeCreate&&ar(t.beforeCreate,e,"bc");const{data:s,computed:l,methods:a,watch:i,provide:c,inject:u,created:p,beforeMount:d,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeDestroy:k,beforeUnmount:w,destroyed:x,unmounted:S,render:C,renderTracked:L,renderTriggered:O,errorCaptured:E,serverPrefetch:F,expose:T,inheritAttrs:P,components:I,directives:$,filters:R}=t;if(u&&function(e,t){f(e)&&(e=dr(e));for(const n in e){const r=e[n];let o;o=_(r)?"default"in r?wr(r.from||n,r.default,!0):wr(r.from||n):wr(r),kt(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[n]=o}}(u,o,null),a)for(const r in a){const e=a[r];m(e)&&(o[r]=e.bind(n))}if(s){const t=s.call(n,n);_(t)&&(e.data=ct(t))}if(sr=!0,l)for(const f in l){const e=l[f],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):r,s=!m(e)&&m(e.set)?e.set.bind(n):r,a=Yo({get:t,set:s});Object.defineProperty(o,f,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(i)for(const r in i)ir(i[r],o,n,r);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{kr(t,e[t])})}function M(e,t){f(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(p&&ar(p,e,"c"),M(Mn,d),M(An,h),M(jn,g),M(Nn,v),M(Fn,y),M(Tn,b),M(Bn,E),M(Hn,L),M(Un,O),M(Wn,w),M(Dn,S),M(Vn,F),f(T))if(T.length){const t=e.exposed||(e.exposed={});T.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});C&&e.render===r&&(e.render=C),null!=P&&(e.inheritAttrs=P),I&&(e.components=I),$&&(e.directives=$),F&&Cn(e)}function ar(e,t,n){Dt(f(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function ir(e,t,n,r){let o=r.includes(".")?Kr(n,r):()=>n[r];if(g(e)){const n=t[e];m(n)&&zr(o,n)}else if(m(e))zr(o,e.bind(n));else if(_(e))if(f(e))e.forEach(e=>ir(e,t,n,r));else{const r=m(e.handler)?e.handler.bind(n):t[e.handler];m(r)&&zr(o,r,e)}}function cr(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:l}}=e.appContext,a=s.get(t);let i;return a?i=a:o.length||n||r?(i={},o.length&&o.forEach(e=>ur(i,e,l,!0)),ur(i,t,l)):i=t,_(t)&&s.set(t,i),i}function ur(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&ur(e,s,n,!0),o&&o.forEach(t=>ur(e,t,n,!0));for(const l in t)if(r&&"expose"===l);else{const r=fr[l]||n&&n[l];e[l]=r?r(e[l],t[l]):t[l]}return e}const fr={data:pr,props:gr,emits:gr,methods:mr,computed:mr,beforeCreate:hr,created:hr,beforeMount:hr,mounted:hr,beforeUpdate:hr,updated:hr,beforeDestroy:hr,beforeUnmount:hr,destroyed:hr,unmounted:hr,activated:hr,deactivated:hr,errorCaptured:hr,serverPrefetch:hr,components:mr,directives:mr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=a(Object.create(null),e);for(const r in t)n[r]=hr(e[r],t[r]);return n},provide:pr,inject:function(e,t){return mr(dr(e),dr(t))}};function pr(e,t){return t?e?function(){return a(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function dr(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function hr(e,t){return e?[...new Set([].concat(e,t))]:t}function mr(e,t){return e?a(Object.create(null),e,t):t}function gr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:a(Object.create(null),or(e),or(null!=t?t:{})):t}function vr(){return{app:null,config:{isNativeTag:o,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let _r=0;function yr(e,t){return function(t,n=null){m(t)||(t=a({},t)),null==n||_(n)||(n=null);const r=vr(),o=new WeakSet,s=[];let l=!1;const i=r.app={_uid:_r++,_component:t,_props:n,_container:null,_context:r,_instance:null,version:Ko,get config(){return r.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&m(e.install)?(o.add(e),e.install(i,...t)):m(e)&&(o.add(e),e(i,...t))),i),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),i),component:(e,t)=>t?(r.components[e]=t,i):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,i):r.directives[e],mount(o,s,a){if(!l){const s=i._ceVNode||xo(t,n);return s.appContext=r,!0===a?a="svg":!1===a&&(a=void 0),e(s,o,a),l=!0,i._container=o,o.__vue_app__=i,Go(s.component)}},onUnmount(e){s.push(e)},unmount(){l&&(Dt(s,i._instance,16),e(null,i._container),delete i._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,i),runWithContext(e){const t=br;br=i;try{return e()}finally{br=t}}};return i}}let br=null;function kr(e,t){if(Ro){let n=Ro.provides;const r=Ro.parent&&Ro.parent.provides;r===n&&(n=Ro.provides=Object.create(r)),n[e]=t}else;}function wr(e,t,n=!1){const r=Ro||nn;if(r||br){let o=br?br._context.provides:r?null==r.parent||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&m(t)?t.call(r&&r.proxy):t}}const xr={},Sr=()=>Object.create(xr),Cr=e=>Object.getPrototypeOf(e)===xr;function Lr(e,n,r,o){const[s,l]=e.propsOptions;let a,i=!1;if(n)for(let t in n){if(S(t))continue;const c=n[t];let f;s&&u(s,f=O(t))?l&&l.includes(f)?(a||(a={}))[f]=c:r[f]=c:Qr(e.emitsOptions,t)||t in o&&c===o[t]||(o[t]=c,i=!0)}if(l){const n=vt(r),o=a||t;for(let t=0;t<l.length;t++){const a=l[t];r[a]=Or(s,n,a,o[a],e,!u(o,a))}}return i}function Or(e,t,n,r,o,s){const l=e[n];if(null!=l){const e=u(l,"default");if(e&&void 0===r){const e=l.default;if(l.type!==Function&&!l.skipFactory&&m(e)){const{propsDefaults:s}=o;if(n in s)r=s[n];else{const l=No(o);r=s[n]=e.call(null,t),l()}}else r=e;o.ce&&o.ce._setProp(n,r)}l[0]&&(s&&!e?r=!1:!l[1]||""!==r&&r!==F(n)||(r=!0))}return r}const Er=new WeakMap;function Fr(e,r,o=!1){const s=o?Er:r.propsCache,l=s.get(e);if(l)return l;const i=e.props,c={},p=[];let d=!1;if(!m(e)){const t=e=>{d=!0;const[t,n]=Fr(e,r,!0);a(c,t),n&&p.push(...n)};!o&&r.mixins.length&&r.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!i&&!d)return _(e)&&s.set(e,n),n;if(f(i))for(let n=0;n<i.length;n++){const e=O(i[n]);Tr(e)&&(c[e]=t)}else if(i)for(const t in i){const e=O(t);if(Tr(e)){const n=i[t],r=c[e]=f(n)||m(n)?{type:n}:a({},n),o=r.type;let s=!1,l=!0;if(f(o))for(let e=0;e<o.length;++e){const t=o[e],n=m(t)&&t.name;if("Boolean"===n){s=!0;break}"String"===n&&(l=!1)}else s=m(o)&&"Boolean"===o.name;r[0]=s,r[1]=l,(s||u(r,"default"))&&p.push(e)}}const h=[c,p];return _(e)&&s.set(e,h),h}function Tr(e){return"$"!==e[0]&&!S(e)}const Pr=e=>"_"===e[0]||"$stable"===e,Ir=e=>f(e)?e.map(Eo):[Eo(e)],$r=(e,t,n)=>{if(t._n)return t;const r=sn((...e)=>Ir(t(...e)),n);return r._c=!1,r},Rr=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Pr(o))continue;const n=e[o];if(m(n))t[o]=$r(0,n,r);else if(null!=n){const e=Ir(n);t[o]=()=>e}}},Mr=(e,t)=>{const n=Ir(t);e.slots.default=()=>n},Ar=(e,t,n)=>{for(const r in t)!n&&Pr(r)||(e[r]=t[r])},jr=function(e,t){t&&t.pendingBranch?f(e)?t.effects.push(...e):t.effects.push(e):(f(n=e)?Bt.push(...n):Gt&&-1===n.id?Gt.splice(zt+1,0,n):1&n.flags||(Bt.push(n),n.flags|=1),Zt());var n};function Nr(e){return function(e){j().__VUE__=!0;const{insert:o,remove:s,patchProp:l,createElement:a,createText:i,createComment:c,setText:p,setElementText:d,parentNode:h,nextSibling:m,setScopeId:g=r,insertStaticContent:v}=e,_=(e,t,n,r=null,o=null,s=null,l=void 0,a=null,i=!!t.dynamicChildren)=>{if(e===t)return;e&&!yo(e,t)&&(r=Q(e),q(e,o,s,!0),e=null),-2===t.patchFlag&&(i=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case lo:b(e,t,n,r);break;case ao:k(e,t,n,r);break;case io:null==e&&w(t,n,r,l);break;case so:N(e,t,n,r,o,s,l,a,i);break;default:1&f?L(e,t,n,r,o,s,l,a,i):6&f?W(e,t,n,r,o,s,l,a,i):(64&f||128&f)&&c.process(e,t,n,r,o,s,l,a,i,re)}null!=u&&o?Ln(u,e&&e.ref,s,t||e,!t):null==u&&e&&null!=e.ref&&Ln(e.ref,null,s,e,!0)},b=(e,t,n,r)=>{if(null==e)o(t.el=i(t.children),n,r);else{const n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},k=(e,t,n,r)=>{null==e?o(t.el=c(t.children||""),n,r):t.el=e.el},w=(e,t,n,r)=>{[e.el,e.anchor]=v(e.children,t,n,r,e.el,e.anchor)},x=({el:e,anchor:t},n,r)=>{let s;for(;e&&e!==t;)s=m(e),o(e,n,r),e=s;o(t,n,r)},C=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=m(e),s(e),e=n;s(t)},L=(e,t,n,r,o,s,l,a,i)=>{"svg"===t.type?l="svg":"math"===t.type&&(l="mathml"),null==e?E(t,n,r,o,s,l,a,i):I(e,t,o,s,l,a,i)},E=(e,t,n,r,s,i,c,u)=>{let f,p;const{props:h,shapeFlag:m,transition:g,dirs:v}=e;if(f=e.el=a(e.type,i,h&&h.is,h),8&m?d(f,e.children):16&m&&P(e.children,f,null,r,s,Wr(e,i),c,u),v&&an(e,null,r,"created"),T(f,e,e.scopeId,c,r),h){for(const e in h)"value"===e||S(e)||l(f,e,null,h[e],i,r);"value"in h&&l(f,"value",null,h.value,i),(p=h.onVnodeBeforeMount)&&Po(p,r,e)}v&&an(e,null,r,"beforeMount");const _=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,g);_&&g.beforeEnter(f),o(f,t,n),((p=h&&h.onVnodeMounted)||_||v)&&jr(()=>{p&&Po(p,r,e),_&&g.enter(f),v&&an(e,null,r,"mounted")},s)},T=(e,t,n,r,o)=>{if(n&&g(e,n),r)for(let s=0;s<r.length;s++)g(e,r[s]);if(o){let n=o.subTree;if(t===n||oo(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=o.vnode;T(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},P=(e,t,n,r,o,s,l,a,i=0)=>{for(let c=i;c<e.length;c++){const i=e[c]=a?Fo(e[c]):Eo(e[c]);_(null,i,t,n,r,o,s,l,a)}},I=(e,n,r,o,s,a,i)=>{const c=n.el=e.el;let{patchFlag:u,dynamicChildren:f,dirs:p}=n;u|=16&e.patchFlag;const h=e.props||t,m=n.props||t;let g;if(r&&Dr(r,!1),(g=m.onVnodeBeforeUpdate)&&Po(g,r,n,e),p&&an(n,e,r,"beforeUpdate"),r&&Dr(r,!0),(h.innerHTML&&null==m.innerHTML||h.textContent&&null==m.textContent)&&d(c,""),f?M(e.dynamicChildren,f,c,r,o,Wr(n,s),a):i||B(e,n,c,null,r,o,Wr(n,s),a,!1),u>0){if(16&u)A(c,h,m,r,s);else if(2&u&&h.class!==m.class&&l(c,"class",null,m.class,s),4&u&&l(c,"style",h.style,m.style,s),8&u){const e=n.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t],o=h[n],a=m[n];a===o&&"value"!==n||l(c,n,o,a,s,r)}}1&u&&e.children!==n.children&&d(c,n.children)}else i||null!=f||A(c,h,m,r,s);((g=m.onVnodeUpdated)||p)&&jr(()=>{g&&Po(g,r,n,e),p&&an(n,e,r,"updated")},o)},M=(e,t,n,r,o,s,l)=>{for(let a=0;a<t.length;a++){const i=e[a],c=t[a],u=i.el&&(i.type===so||!yo(i,c)||198&i.shapeFlag)?h(i.el):n;_(i,c,u,null,r,o,s,l,!0)}},A=(e,n,r,o,s)=>{if(n!==r){if(n!==t)for(const t in n)S(t)||t in r||l(e,t,n[t],null,s,o);for(const t in r){if(S(t))continue;const a=r[t],i=n[t];a!==i&&"value"!==t&&l(e,t,i,a,s,o)}"value"in r&&l(e,"value",n.value,r.value,s)}},N=(e,t,n,r,s,l,a,c,u)=>{const f=t.el=e?e.el:i(""),p=t.anchor=e?e.anchor:i("");let{patchFlag:d,dynamicChildren:h,slotScopeIds:m}=t;m&&(c=c?c.concat(m):m),null==e?(o(f,n,r),o(p,n,r),P(t.children||[],n,p,s,l,a,c,u)):d>0&&64&d&&h&&e.dynamicChildren?(M(e.dynamicChildren,h,n,s,l,a,c),(null!=t.key||s&&t===s.subTree)&&Vr(e,t,!0)):B(e,t,n,p,s,l,a,c,u)},W=(e,t,n,r,o,s,l,a,i)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,l,i):D(t,n,r,o,s,l,i):V(e,t,i)},D=(e,n,r,o,s,l,a)=>{const i=e.component=function(e,n,r){const o=e.type,s=(n?n.appContext:e.appContext)||Io,l={uid:$o++,vnode:e,type:o,parent:n,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ee(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(s.provides),ids:n?n.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Fr(o,s),emitsOptions:Xr(o,s),emit:null,emitted:null,propsDefaults:t,inheritAttrs:o.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};l.ctx={_:l},l.root=n?n.root:l,l.emit=Zr.bind(null,l),e.ce&&e.ce(l);return l}(e,o,s);if(En(e)&&(i.ctx.renderer=re),function(e,t=!1,n=!1){t&&jo(t);const{props:r,children:o}=e.vnode,s=Do(e);(function(e,t,n,r=!1){const o={},s=Sr();e.propsDefaults=Object.create(null),Lr(e,t,o,s);for(const l in e.propsOptions[0])l in o||(o[l]=void 0);n?e.props=r?o:ut(o):e.type.props?e.props=o:e.props=s,e.attrs=s})(e,r,s,t),((e,t,n)=>{const r=e.slots=Sr();if(32&e.vnode.shapeFlag){const e=t.__;e&&R(r,"__",e,!0);const o=t._;o?(Ar(r,t,n),n&&R(r,"_",o,!0)):Rr(t,r)}else t&&Mr(e,t)})(e,o,n||t);const l=s?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,rr);const{setup:r}=n;if(r){ye();const n=e.setupContext=r.length>1?function(e){const t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,Bo),slots:e.slots,emit:e.emit,expose:t}}(e):null,o=No(e),s=Wt(r,e,0,[e.props,n]),l=y(s);if(be(),o(),!l&&!e.sp||On(e)||Cn(e),l){if(s.then(Wo,Wo),t)return s.then(t=>{Uo(e,t)}).catch(t=>{Vt(t,e,0)});e.asyncDep=s}else Uo(e,s)}else Ho(e)}(e,t):void 0;t&&jo(!1)}(i,!1,a),i.asyncDep){if(s&&s.registerDep(i,U,a),!e.el){const e=i.subTree=xo(ao);k(null,e,n,r)}}else U(i,e,n,r,s,l,a)},V=(e,t,n)=>{const r=t.component=e.component;if(function(e,t,n){const{props:r,children:o,component:s}=e,{props:l,children:a,patchFlag:i}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&i>=0))return!(!o&&!a||a&&a.$stable)||r!==l&&(r?!l||ro(r,l,c):!!l);if(1024&i)return!0;if(16&i)return r?ro(r,l,c):!!l;if(8&i){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(l[n]!==r[n]&&!Qr(c,n))return!0}}return!1}(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void H(r,t,n);r.next=t,r.update()}else t.el=e.el,r.vnode=t},U=(e,t,n,r,o,s,l)=>{const a=()=>{if(e.isMounted){let{next:t,bu:n,u:r,parent:i,vnode:c}=e;{const n=Ur(e);if(n)return t&&(t.el=c.el,H(e,t,l)),void n.asyncDep.then(()=>{e.isUnmounted||a()})}let u,f=t;Dr(e,!1),t?(t.el=c.el,H(e,t,l)):t=c,n&&$(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Po(u,i,t,c),Dr(e,!0);const p=eo(e),d=e.subTree;e.subTree=p,_(d,p,h(d.el),Q(d),e,o,s),t.el=p.el,null===f&&function({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,p.el),r&&jr(r,o),(u=t.props&&t.props.onVnodeUpdated)&&jr(()=>Po(u,i,t,c),o)}else{let l;const{el:a,props:i}=t,{bm:c,m:u,parent:f,root:p,type:d}=e,h=On(t);Dr(e,!1),c&&$(c),!h&&(l=i&&i.onVnodeBeforeMount)&&Po(l,f,t),Dr(e,!0);{p.ce&&!1!==p.ce._def.shadowRoot&&p.ce._injectChildStyle(d);const l=e.subTree=eo(e);_(null,l,n,r,e,o,s),t.el=l.el}if(u&&jr(u,o),!h&&(l=i&&i.onVnodeMounted)){const e=t;jr(()=>Po(l,f,e),o)}(256&t.shapeFlag||f&&On(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&jr(e.a,o),e.isMounted=!0,t=n=r=null}};e.scope.on();const i=e.effect=new oe(a);e.scope.off();const c=e.update=i.run.bind(i),u=e.job=i.runIfDirty.bind(i);u.i=e,u.id=e.uid,i.scheduler=()=>Jt(u),Dr(e,!0),c()},H=(e,n,r)=>{n.component=e;const o=e.vnode.props;e.vnode=n,e.next=null,function(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:l}}=e,a=vt(o),[i]=e.propsOptions;let c=!1;if(!(r||l>0)||16&l){let r;Lr(e,t,o,s)&&(c=!0);for(const s in a)t&&(u(t,s)||(r=F(s))!==s&&u(t,r))||(i?!n||void 0===n[s]&&void 0===n[r]||(o[s]=Or(i,a,s,void 0,e,!0)):delete o[s]);if(s!==a)for(const e in s)t&&u(t,e)||(delete s[e],c=!0)}else if(8&l){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let l=n[r];if(Qr(e.emitsOptions,l))continue;const f=t[l];if(i)if(u(s,l))f!==s[l]&&(s[l]=f,c=!0);else{const t=O(l);o[t]=Or(i,a,t,f,e,!1)}else f!==s[l]&&(s[l]=f,c=!0)}}c&&Pe(e.attrs,"set","")}(e,n.props,o,r),((e,n,r)=>{const{vnode:o,slots:s}=e;let l=!0,a=t;if(32&o.shapeFlag){const e=n._;e?r&&1===e?l=!1:Ar(s,n,r):(l=!n.$stable,Rr(n,s)),a=n}else n&&(Mr(e,n),a={default:1});if(l)for(const t in s)Pr(t)||null!=a[t]||delete s[t]})(e,n.children,r),ye(),Xt(e),be()},B=(e,t,n,r,o,s,l,a,i=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,f=t.children,{patchFlag:p,shapeFlag:h}=t;if(p>0){if(128&p)return void z(c,f,n,r,o,s,l,a,i);if(256&p)return void G(c,f,n,r,o,s,l,a,i)}8&h?(16&u&&X(c,o,s),f!==c&&d(n,f)):16&u?16&h?z(c,f,n,r,o,s,l,a,i):X(c,o,s,!0):(8&u&&d(n,""),16&h&&P(f,n,r,o,s,l,a,i))},G=(e,t,r,o,s,l,a,i,c)=>{t=t||n;const u=(e=e||n).length,f=t.length,p=Math.min(u,f);let d;for(d=0;d<p;d++){const n=t[d]=c?Fo(t[d]):Eo(t[d]);_(e[d],n,r,null,s,l,a,i,c)}u>f?X(e,s,l,!0,!1,p):P(t,r,o,s,l,a,i,c,p)},z=(e,t,r,o,s,l,a,i,c)=>{let u=0;const f=t.length;let p=e.length-1,d=f-1;for(;u<=p&&u<=d;){const n=e[u],o=t[u]=c?Fo(t[u]):Eo(t[u]);if(!yo(n,o))break;_(n,o,r,null,s,l,a,i,c),u++}for(;u<=p&&u<=d;){const n=e[p],o=t[d]=c?Fo(t[d]):Eo(t[d]);if(!yo(n,o))break;_(n,o,r,null,s,l,a,i,c),p--,d--}if(u>p){if(u<=d){const e=d+1,n=e<f?t[e].el:o;for(;u<=d;)_(null,t[u]=c?Fo(t[u]):Eo(t[u]),r,n,s,l,a,i,c),u++}}else if(u>d)for(;u<=p;)q(e[u],s,l,!0),u++;else{const h=u,m=u,g=new Map;for(u=m;u<=d;u++){const e=t[u]=c?Fo(t[u]):Eo(t[u]);null!=e.key&&g.set(e.key,u)}let v,y=0;const b=d-m+1;let k=!1,w=0;const x=new Array(b);for(u=0;u<b;u++)x[u]=0;for(u=h;u<=p;u++){const n=e[u];if(y>=b){q(n,s,l,!0);continue}let o;if(null!=n.key)o=g.get(n.key);else for(v=m;v<=d;v++)if(0===x[v-m]&&yo(n,t[v])){o=v;break}void 0===o?q(n,s,l,!0):(x[o-m]=u+1,o>=w?w=o:k=!0,_(n,t[o],r,null,s,l,a,i,c),y++)}const S=k?function(e){const t=e.slice(),n=[0];let r,o,s,l,a;const i=e.length;for(r=0;r<i;r++){const i=e[r];if(0!==i){if(o=n[n.length-1],e[o]<i){t[r]=o,n.push(r);continue}for(s=0,l=n.length-1;s<l;)a=s+l>>1,e[n[a]]<i?s=a+1:l=a;i<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}s=n.length,l=n[s-1];for(;s-- >0;)n[s]=l,l=t[l];return n}(x):n;for(v=S.length-1,u=b-1;u>=0;u--){const e=m+u,n=t[e],p=e+1<f?t[e+1].el:o;0===x[u]?_(null,n,r,p,s,l,a,i,c):k&&(v<0||u!==S[v]?Y(n,r,p,2):v--)}}},Y=(e,t,n,r,l=null)=>{const{el:a,type:i,transition:c,children:u,shapeFlag:f}=e;if(6&f)return void Y(e.component.subTree,t,n,r);if(128&f)return void e.suspense.move(t,n,r);if(64&f)return void i.move(e,t,n,re);if(i===so){o(a,t,n);for(let e=0;e<u.length;e++)Y(u[e],t,n,r);return void o(e.anchor,t,n)}if(i===io)return void x(e,t,n);if(2!==r&&1&f&&c)if(0===r)c.beforeEnter(a),o(a,t,n),jr(()=>c.enter(a),l);else{const{leave:r,delayLeave:l,afterLeave:i}=c,u=()=>{e.ctx.isUnmounted?s(a):o(a,t,n)},f=()=>{r(a,()=>{u(),i&&i()})};l?l(a,u,f):f()}else o(a,t,n)},q=(e,t,n,r=!1,o=!1)=>{const{type:s,props:l,ref:a,children:i,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:p,cacheIndex:d}=e;if(-2===f&&(o=!1),null!=a&&(ye(),Ln(a,null,n,e,!0),be()),null!=d&&(t.renderCache[d]=void 0),256&u)return void t.ctx.deactivate(e);const h=1&u&&p,m=!On(e);let g;if(m&&(g=l&&l.onVnodeBeforeUnmount)&&Po(g,t,e),6&u)Z(e.component,n,r);else{if(128&u)return void e.suspense.unmount(n,r);h&&an(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,re,r):c&&!c.hasOnce&&(s!==so||f>0&&64&f)?X(c,t,n,!1,!0):(s===so&&384&f||!o&&16&u)&&X(i,t,n),r&&K(e)}(m&&(g=l&&l.onVnodeUnmounted)||h)&&jr(()=>{g&&Po(g,t,e),h&&an(e,null,t,"unmounted")},n)},K=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===so)return void J(n,r);if(t===io)return void C(e);const l=()=>{s(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:r}=o,s=()=>t(n,l);r?r(e.el,l,s):s()}else l()},J=(e,t)=>{let n;for(;e!==t;)n=m(e),s(e),e=n;s(t)},Z=(e,t,n)=>{const{bum:r,scope:o,job:s,subTree:l,um:a,m:i,a:c,parent:u,slots:{__:p}}=e;Hr(i),Hr(c),r&&$(r),u&&f(p)&&p.forEach(e=>{u.renderCache[e]=void 0}),o.stop(),s&&(s.flags|=8,q(l,e,t,n)),a&&jr(a,t),jr(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},X=(e,t,n,r=!1,o=!1,s=0)=>{for(let l=s;l<e.length;l++)q(e[l],t,n,r,o)},Q=e=>{if(6&e.shapeFlag)return Q(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=m(e.anchor||e.el),n=t&&t[cn];return n?m(n):t};let te=!1;const ne=(e,t,n)=>{null==e?t._vnode&&q(t._vnode,null,null,!0):_(t._vnode||null,e,t,null,null,null,n),t._vnode=e,te||(te=!0,Xt(),Qt(),te=!1)},re={p:_,um:q,m:Y,r:K,mt:D,mc:P,pc:B,pbc:M,n:Q,o:e};let se;return{render:ne,hydrate:se,createApp:yr(ne)}}(e)}function Wr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Dr({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Vr(e,t,n=!1){const r=e.children,o=t.children;if(f(r)&&f(o))for(let s=0;s<r.length;s++){const e=r[s];let t=o[s];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=o[s]=Fo(o[s]),t.el=e.el),n||-2===t.patchFlag||Vr(e,t)),t.type===lo&&(t.el=e.el),t.type!==ao||t.el||(t.el=e.el)}}function Ur(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ur(t)}function Hr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Br=Symbol.for("v-scx"),Gr=()=>wr(Br);function zr(e,t,n){return Yr(e,t,n)}function Yr(e,n,o=t){const{immediate:s,deep:l,flush:i,once:c}=o,u=a({},o),f=n&&s||!n&&"post"!==i;let p;if(Vo)if("sync"===i){const e=Gr();p=e.__watcherHandles||(e.__watcherHandles=[])}else if(!f){const e=()=>{};return e.stop=r,e.resume=r,e.pause=r,e}const d=Ro;u.call=(e,t,n)=>Dt(e,d,t,n);let h=!1;"post"===i?u.scheduler=e=>{jr(e,d&&d.suspense)}:"sync"!==i&&(h=!0,u.scheduler=(e,t)=>{t?e():Jt(e)}),u.augmentJob=e=>{n&&(e.flags|=4),h&&(e.flags|=2,d&&(e.id=d.uid,e.i=d))};const m=jt(e,n,u);return Vo&&(p?p.push(m):f&&m()),m}function qr(e,t,n){const r=this.proxy,o=g(e)?e.includes(".")?Kr(r,e):()=>r[e]:e.bind(r,r);let s;m(t)?s=t:(s=t.handler,n=t);const l=No(this),a=Yr(o,s.bind(r),n);return l(),a}function Kr(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const Jr=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${O(t)}Modifiers`]||e[`${F(t)}Modifiers`];function Zr(e,n,...r){if(e.isUnmounted)return;const o=e.vnode.props||t;let s=r;const l=n.startsWith("update:"),a=l&&Jr(o,n.slice(7));let i;a&&(a.trim&&(s=r.map(e=>g(e)?e.trim():e)),a.number&&(s=r.map(M)));let c=o[i=P(n)]||o[i=P(O(n))];!c&&l&&(c=o[i=P(F(n))]),c&&Dt(c,e,6,s);const u=o[i+"Once"];if(u){if(e.emitted){if(e.emitted[i])return}else e.emitted={};e.emitted[i]=!0,Dt(u,e,6,s)}}function Xr(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;const s=e.emits;let l={},i=!1;if(!m(e)){const r=e=>{const n=Xr(e,t,!0);n&&(i=!0,a(l,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return s||i?(f(s)?s.forEach(e=>l[e]=null):a(l,s),_(e)&&r.set(e,l),l):(_(e)&&r.set(e,null),null)}function Qr(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),u(e,t[0].toLowerCase()+t.slice(1))||u(e,F(t))||u(e,t))}function eo(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[s],slots:a,attrs:i,emit:c,render:u,renderCache:f,props:p,data:d,setupState:h,ctx:m,inheritAttrs:g}=e,v=on(e);let _,y;try{if(4&n.shapeFlag){const e=o||r,t=e;_=Eo(u.call(t,e,f,p,h,d,m)),y=i}else{const e=t;0,_=Eo(e.length>1?e(p,{attrs:i,slots:a,emit:c}):e(p,null)),y=t.props?i:to(i)}}catch(k){co.length=0,Vt(k,e,1),_=xo(ao)}let b=_;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(l)&&(y=no(y,s)),b=So(b,y,!1,!0))}return n.dirs&&(b=So(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&wn(b,n.transition),_=b,on(v),_}const to=e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t},no=(e,t)=>{const n={};for(const r in e)l(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function ro(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Qr(n,s))return!0}return!1}const oo=e=>e.__isSuspense;const so=Symbol.for("v-fgt"),lo=Symbol.for("v-txt"),ao=Symbol.for("v-cmt"),io=Symbol.for("v-stc"),co=[];let uo=null;function fo(e=!1){co.push(uo=e?null:[])}let po=1;function ho(e,t=!1){po+=e,e<0&&uo&&t&&(uo.hasOnce=!0)}function mo(e){return e.dynamicChildren=po>0?uo||n:null,co.pop(),uo=co[co.length-1]||null,po>0&&uo&&uo.push(e),e}function go(e,t,n,r,o,s){return mo(wo(e,t,n,r,o,s,!0))}function vo(e,t,n,r,o){return mo(xo(e,t,n,r,o,!0))}function _o(e){return!!e&&!0===e.__v_isVNode}function yo(e,t){return e.type===t.type&&e.key===t.key}const bo=({key:e})=>null!=e?e:null,ko=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?g(e)||kt(e)||m(e)?{i:nn,r:e,k:t,f:!!n}:e:null);function wo(e,t=null,n=null,r=0,o=null,s=(e===so?0:1),l=!1,a=!1){const i={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&bo(t),ref:t&&ko(t),scopeId:rn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:nn};return a?(To(i,n),128&s&&e.normalize(i)):n&&(i.shapeFlag|=g(n)?8:16),po>0&&!l&&uo&&(i.patchFlag>0||6&s)&&32!==i.patchFlag&&uo.push(i),i}const xo=function(e,t=null,n=null,r=0,o=null,s=!1){e&&e!==Yn||(e=ao);if(_o(e)){const r=So(e,t,!0);return n&&To(r,n),po>0&&!s&&uo&&(6&r.shapeFlag?uo[uo.indexOf(e)]=r:uo.push(r)),r.patchFlag=-2,r}l=e,m(l)&&"__vccOpts"in l&&(e=e.__vccOpts);var l;if(t){t=function(e){return e?gt(e)||Cr(e)?a({},e):e:null}(t);let{class:e,style:n}=t;e&&!g(e)&&(t.class=H(e)),_(n)&&(gt(n)&&!f(n)&&(n=a({},n)),t.style=N(n))}const i=g(e)?1:oo(e)?128:un(e)?64:_(e)?4:m(e)?2:0;return wo(e,t,n,r,o,i,s,!0)};function So(e,t,n=!1,r=!1){const{props:o,ref:l,patchFlag:a,children:i,transition:c}=e,u=t?function(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=H([t.class,r.class]));else if("style"===e)t.style=N([t.style,r.style]);else if(s(e)){const n=t[e],o=r[e];!o||n===o||f(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}(o||{},t):o,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&bo(u),ref:t&&t.ref?n&&l?f(l)?l.concat(ko(t)):[l,ko(t)]:ko(t):l,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:i,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==so?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&So(e.ssContent),ssFallback:e.ssFallback&&So(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&wn(p,c.clone(p)),p}function Co(e=" ",t=0){return xo(lo,null,e,t)}function Lo(e,t){const n=xo(io,null,e);return n.staticCount=t,n}function Oo(e="",t=!1){return t?(fo(),vo(ao,null,e)):xo(ao,null,e)}function Eo(e){return null==e||"boolean"==typeof e?xo(ao):f(e)?xo(so,null,e.slice()):_o(e)?Fo(e):xo(lo,null,String(e))}function Fo(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:So(e)}function To(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if(f(t))n=16;else if("object"==typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),To(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||Cr(t)?3===r&&nn&&(1===nn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=nn}}else m(t)?(t={default:t,_ctx:nn},n=32):(t=String(t),64&r?(n=16,t=[Co(t)]):n=8);e.children=t,e.shapeFlag|=n}function Po(e,t,n,r=null){Dt(e,t,7,[n,r])}const Io=vr();let $o=0;let Ro=null;const Mo=()=>Ro||nn;let Ao,jo;{const e=j(),t=(t,n)=>{let r;return(r=e[t])||(r=e[t]=[]),r.push(n),e=>{r.length>1?r.forEach(t=>t(e)):r[0](e)}};Ao=t("__VUE_INSTANCE_SETTERS__",e=>Ro=e),jo=t("__VUE_SSR_SETTERS__",e=>Vo=e)}const No=e=>{const t=Ro;return Ao(e),e.scope.on(),()=>{e.scope.off(),Ao(t)}},Wo=()=>{Ro&&Ro.scope.off(),Ao(null)};function Do(e){return 4&e.vnode.shapeFlag}let Vo=!1;function Uo(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=Et(t)),Ho(e)}function Ho(e,t,n){const o=e.type;e.render||(e.render=o.render||r);{const t=No(e);ye();try{lr(e)}finally{be(),t()}}}const Bo={get:(e,t)=>(Te(e,0,""),e[t])};function Go(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Et(_t(e.exposed)),{get:(t,n)=>n in t?t[n]:n in tr?tr[n](e):void 0,has:(e,t)=>t in e||t in tr})):e.proxy}function zo(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const Yo=(e,t)=>{const n=function(e,t,n=!1){let r,o;return m(e)?r=e:(r=e.get,o=e.set),new $t(r,o,n)}(e,0,Vo);return n};function qo(e,t,n){const r=arguments.length;return 2===r?_(t)&&!f(t)?_o(t)?xo(e,null,[t]):xo(e,t):xo(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&_o(n)&&(n=[n]),xo(e,t,n))}const Ko="3.5.17";
/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Jo;const Zo="undefined"!=typeof window&&window.trustedTypes;if(Zo)try{Jo=Zo.createPolicy("vue",{createHTML:e=>e})}catch(If){}const Xo=Jo?e=>Jo.createHTML(e):e=>e,Qo="undefined"!=typeof document?document:null,es=Qo&&Qo.createElement("template"),ts={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o="svg"===t?Qo.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Qo.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Qo.createElement(e,{is:n}):Qo.createElement(e);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>Qo.createTextNode(e),createComment:e=>Qo.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Qo.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const l=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==s&&(o=o.nextSibling););else{es.innerHTML=Xo("svg"===r?`<svg>${e}</svg>`:"mathml"===r?`<math>${e}</math>`:e);const o=es.content;if("svg"===r||"mathml"===r){const e=o.firstChild;for(;e.firstChild;)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ns="transition",rs="animation",os=Symbol("_vtc"),ss={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},ls=a({},hn,ss),as=(e=>(e.displayName="Transition",e.props=ls,e))((e,{slots:t})=>qo(vn,function(e){const t={};for(const a in e)a in ss||(t[a]=e[a]);if(!1===e.css)return t;const{name:n="v",type:r,duration:o,enterFromClass:s=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:i=`${n}-enter-to`,appearFromClass:c=s,appearActiveClass:u=l,appearToClass:f=i,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(_(e))return[us(e.enter),us(e.leave)];{const t=us(e);return[t,t]}}(o),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:k,onLeave:w,onLeaveCancelled:x,onBeforeAppear:S=y,onAppear:C=b,onAppearCancelled:L=k}=t,O=(e,t,n,r)=>{e._enterCancelled=r,ps(e,t?f:i),ps(e,t?u:l),n&&n()},E=(e,t)=>{e._isLeaving=!1,ps(e,p),ps(e,h),ps(e,d),t&&t()},F=e=>(t,n)=>{const o=e?C:b,l=()=>O(t,e,n);is(o,[t,l]),ds(()=>{ps(t,e?c:s),fs(t,e?f:i),cs(o)||ms(t,r,g,l)})};return a(t,{onBeforeEnter(e){is(y,[e]),fs(e,s),fs(e,l)},onBeforeAppear(e){is(S,[e]),fs(e,c),fs(e,u)},onEnter:F(!1),onAppear:F(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>E(e,t);fs(e,p),e._enterCancelled?(fs(e,d),_s()):(_s(),fs(e,d)),ds(()=>{e._isLeaving&&(ps(e,p),fs(e,h),cs(w)||ms(e,r,v,n))}),is(w,[e,n])},onEnterCancelled(e){O(e,!1,void 0,!0),is(k,[e])},onAppearCancelled(e){O(e,!0,void 0,!0),is(L,[e])},onLeaveCancelled(e){E(e),is(x,[e])}})}(e),t)),is=(e,t=[])=>{f(e)?e.forEach(e=>e(...t)):e&&e(...t)},cs=e=>!!e&&(f(e)?e.some(e=>e.length>1):e.length>1);function us(e){const t=(e=>{const t=g(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function fs(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e[os]||(e[os]=new Set)).add(t)}function ps(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const n=e[os];n&&(n.delete(t),n.size||(e[os]=void 0))}function ds(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let hs=0;function ms(e,t,n,r){const o=e._endId=++hs,s=()=>{o===e._endId&&r()};if(null!=n)return setTimeout(s,n);const{type:l,timeout:a,propCount:i}=function(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),o=r(`${ns}Delay`),s=r(`${ns}Duration`),l=gs(o,s),a=r(`${rs}Delay`),i=r(`${rs}Duration`),c=gs(a,i);let u=null,f=0,p=0;t===ns?l>0&&(u=ns,f=l,p=s.length):t===rs?c>0&&(u=rs,f=c,p=i.length):(f=Math.max(l,c),u=f>0?l>c?ns:rs:null,p=u?u===ns?s.length:i.length:0);const d=u===ns&&/\b(transform|all)(,|$)/.test(r(`${ns}Property`).toString());return{type:u,timeout:f,propCount:p,hasTransform:d}}(e,t);if(!l)return r();const c=l+"end";let u=0;const f=()=>{e.removeEventListener(c,p),s()},p=t=>{t.target===e&&++u>=i&&f()};setTimeout(()=>{u<i&&f()},a+1),e.addEventListener(c,p)}function gs(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>vs(t)+vs(e[n])))}function vs(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function _s(){return document.body.offsetHeight}const ys=Symbol("_vod"),bs=Symbol("_vsh"),ks=Symbol(""),ws=/(^|;)\s*display\s*:/;const xs=/\s*!important$/;function Ss(e,t,n){if(f(n))n.forEach(n=>Ss(e,t,n));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=function(e,t){const n=Ls[t];if(n)return n;let r=O(t);if("filter"!==r&&r in e)return Ls[t]=r;r=T(r);for(let o=0;o<Cs.length;o++){const n=Cs[o]+r;if(n in e)return Ls[t]=n}return t}(e,t);xs.test(n)?e.setProperty(F(r),n.replace(xs,""),"important"):e[r]=n}}const Cs=["Webkit","Moz","ms"],Ls={};const Os="http://www.w3.org/1999/xlink";function Es(e,t,n,r,o,s=B(t)){r&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(Os,t.slice(6,t.length)):e.setAttributeNS(Os,t,n):null==n||s&&!G(n)?e.removeAttribute(t):e.setAttribute(t,s?"":v(n)?String(n):n)}function Fs(e,t,n,r,o){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?Xo(n):n));const s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){const r="OPTION"===s?e.getAttribute("value")||"":e.value,o=null==n?"checkbox"===e.type?"on":"":String(n);return r===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const r=typeof e[t];"boolean"===r?n=G(n):null==n&&"string"===r?(n="",l=!0):"number"===r&&(n=0,l=!0)}try{e[t]=n}catch(If){}l&&e.removeAttribute(o||t)}function Ts(e,t,n,r){e.addEventListener(t,n,r)}const Ps=Symbol("_vei");function Is(e,t,n,r,o=null){const s=e[Ps]||(e[Ps]={}),l=s[t];if(r&&l)l.value=r;else{const[n,a]=function(e){let t;if($s.test(e)){let n;for(t={};n=e.match($s);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):F(e.slice(2));return[n,t]}(t);if(r){const l=s[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Dt(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=As(),n}(r,o);Ts(e,n,l,a)}else l&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,n,l,a),s[t]=void 0)}}const $s=/(?:Once|Passive|Capture)$/;let Rs=0;const Ms=Promise.resolve(),As=()=>Rs||(Ms.then(()=>Rs=0),Rs=Date.now());const js=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const Ns=e=>{const t=e.props["onUpdate:modelValue"]||!1;return f(t)?e=>$(t,e):t};function Ws(e){e.target.composing=!0}function Ds(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Vs=Symbol("_assign"),Us={created(e,{modifiers:{lazy:t,trim:n,number:r}},o){e[Vs]=Ns(o);const s=r||o.props&&"number"===o.props.type;Ts(e,t?"change":"input",t=>{if(t.target.composing)return;let r=e.value;n&&(r=r.trim()),s&&(r=M(r)),e[Vs](r)}),n&&Ts(e,"change",()=>{e.value=e.value.trim()}),t||(Ts(e,"compositionstart",Ws),Ts(e,"compositionend",Ds),Ts(e,"change",Ds))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:o,number:s}},l){if(e[Vs]=Ns(l),e.composing)return;const a=null==t?"":t;if((!s&&"number"!==e.type||/^0\d/.test(e.value)?e.value:M(e.value))!==a){if(document.activeElement===e&&"range"!==e.type){if(r&&t===n)return;if(o&&e.value.trim()===a)return}e.value=a}}},Hs={deep:!0,created(e,t,n){e[Vs]=Ns(n),Ts(e,"change",()=>{const t=e._modelValue,n=qs(e),r=e.checked,o=e[Vs];if(f(t)){const e=Y(t,n),s=-1!==e;if(r&&!s)o(t.concat(n));else if(!r&&s){const n=[...t];n.splice(e,1),o(n)}}else if(d(t)){const e=new Set(t);r?e.add(n):e.delete(n),o(e)}else o(Ks(e,r))})},mounted:Bs,beforeUpdate(e,t,n){e[Vs]=Ns(n),Bs(e,t,n)}};function Bs(e,{value:t,oldValue:n},r){let o;if(e._modelValue=t,f(t))o=Y(t,r.props.value)>-1;else if(d(t))o=t.has(r.props.value);else{if(t===n)return;o=z(t,Ks(e,!0))}e.checked!==o&&(e.checked=o)}const Gs={created(e,{value:t},n){e.checked=z(t,n.props.value),e[Vs]=Ns(n),Ts(e,"change",()=>{e[Vs](qs(e))})},beforeUpdate(e,{value:t,oldValue:n},r){e[Vs]=Ns(r),t!==n&&(e.checked=z(t,r.props.value))}},zs={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const o=d(t);Ts(e,"change",()=>{const t=Array.prototype.filter.call(e.options,e=>e.selected).map(e=>n?M(qs(e)):qs(e));e[Vs](e.multiple?o?new Set(t):t:t[0]),e._assigning=!0,Kt(()=>{e._assigning=!1})}),e[Vs]=Ns(r)},mounted(e,{value:t}){Ys(e,t)},beforeUpdate(e,t,n){e[Vs]=Ns(n)},updated(e,{value:t}){e._assigning||Ys(e,t)}};function Ys(e,t){const n=e.multiple,r=f(t);if(!n||r||d(t)){for(let o=0,s=e.options.length;o<s;o++){const s=e.options[o],l=qs(s);if(n)if(r){const e=typeof l;s.selected="string"===e||"number"===e?t.some(e=>String(e)===String(l)):Y(t,l)>-1}else s.selected=t.has(l);else if(z(qs(s),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function qs(e){return"_value"in e?e._value:e.value}function Ks(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Js={created(e,t,n){Zs(e,t,n,null,"created")},mounted(e,t,n){Zs(e,t,n,null,"mounted")},beforeUpdate(e,t,n,r){Zs(e,t,n,r,"beforeUpdate")},updated(e,t,n,r){Zs(e,t,n,r,"updated")}};function Zs(e,t,n,r,o){const s=function(e,t){switch(e){case"SELECT":return zs;case"TEXTAREA":return Us;default:switch(t){case"checkbox":return Hs;case"radio":return Gs;default:return Us}}}(e.tagName,n.props&&n.props.type)[o];s&&s(e,t,n,r)}const Xs=["ctrl","shift","alt","meta"],Qs={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Xs.some(n=>e[`${n}Key`]&&!t.includes(n))},el=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(n,...r)=>{for(let e=0;e<t.length;e++){const r=Qs[t[e]];if(r&&r(n,t))return}return e(n,...r)})},tl={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},nl=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=n=>{if(!("key"in n))return;const r=F(n.key);return t.some(e=>e===r||tl[e]===r)?e(n):void 0})},rl=a({patchProp:(e,t,n,r,o,a)=>{const i="svg"===o;"class"===t?function(e,t,n){const r=e[os];r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,i):"style"===t?function(e,t,n){const r=e.style,o=g(n);let s=!1;if(n&&!o){if(t)if(g(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Ss(r,t,"")}else for(const e in t)null==n[e]&&Ss(r,e,"");for(const e in n)"display"===e&&(s=!0),Ss(r,e,n[e])}else if(o){if(t!==n){const e=r[ks];e&&(n+=";"+e),r.cssText=n,s=ws.test(n)}}else t&&e.removeAttribute("style");ys in e&&(e[ys]=s?r.display:"",e[bs]&&(r.display="none"))}(e,n,r):s(t)?l(t)||Is(e,t,0,r,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&js(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"autocorrect"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(js(t)&&g(n))return!1;return t in e}(e,t,r,i))?(Fs(e,t,r),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||Es(e,t,r,i,0,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&g(r)?("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),Es(e,t,r,i)):Fs(e,O(t),r,0,t)}},ts);let ol;const sl=(...e)=>{const t=(ol||(ol=Nr(rl))).createApp(...e),{mount:n}=t;return t.mount=e=>{const r=function(e){if(g(e)){return document.querySelector(e)}return e}
/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */(e);if(!r)return;const o=t._component;m(o)||o.render||o.template||(o.template=r.innerHTML),1===r.nodeType&&(r.textContent="");const s=n(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),s},t};let ll;const al=e=>ll=e,il=Symbol();function cl(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var ul,fl;function pl(){const e=te(!0),t=e.run(()=>wt({}));let n=[],r=[];const o=_t({install(e){al(o),o._a=e,e.provide(il,o),e.config.globalProperties.$pinia=o,r.forEach(e=>n.push(e)),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}(fl=ul||(ul={})).direct="direct",fl.patchObject="patch object",fl.patchFunction="patch function";const dl=()=>{};function hl(e,t,n,r=dl){e.push(t);const o=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};var s;return!n&&ne()&&(s=o,X&&X.cleanups.push(s)),o}function ml(e,...t){e.slice().forEach(e=>{e(...t)})}const gl=e=>e(),vl=Symbol(),_l=Symbol();function yl(e,t){e instanceof Map&&t instanceof Map?t.forEach((t,n)=>e.set(n,t)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];cl(o)&&cl(r)&&e.hasOwnProperty(n)&&!kt(r)&&!dt(r)?e[n]=yl(o,r):e[n]=r}return e}const bl=Symbol();function kl(e){return!cl(e)||!Object.prototype.hasOwnProperty.call(e,bl)}const{assign:wl}=Object;function xl(e){return!(!kt(e)||!e.effect)}function Sl(e,t,n,r){const{state:o,actions:s,getters:l}=t,a=n.state.value[e];let i;return i=Cl(e,function(){a||(n.state.value[e]=o?o():{});const t=function(e){const t=f(e)?new Array(e.length):{};for(const n in e)t[n]=It(e,n);return t}(n.state.value[e]);return wl(t,s,Object.keys(l||{}).reduce((t,r)=>(t[r]=_t(Yo(()=>{al(n);const t=n._s.get(e);return l[r].call(t,t)})),t),{}))},t,n,r,!0),i}function Cl(e,t,n={},r,o,s){let l;const a=wl({actions:{}},n),i={deep:!0};let c,u,f,p=[],d=[];const h=r.state.value[e];let m;function g(t){let n;c=u=!1,"function"==typeof t?(t(r.state.value[e]),n={type:ul.patchFunction,storeId:e,events:f}):(yl(r.state.value[e],t),n={type:ul.patchObject,payload:t,storeId:e,events:f});const o=m=Symbol();Kt().then(()=>{m===o&&(c=!0)}),u=!0,ml(p,n,r.state.value[e])}s||h||(r.state.value[e]={}),wt({});const v=s?function(){const{state:e}=n,t=e?e():{};this.$patch(e=>{wl(e,t)})}:dl;const _=(t,n="")=>{if(vl in t)return t[_l]=n,t;const o=function(){al(r);const n=Array.from(arguments),s=[],l=[];let a;ml(d,{args:n,name:o[_l],store:y,after:function(e){s.push(e)},onError:function(e){l.push(e)}});try{a=t.apply(this&&this.$id===e?this:y,n)}catch(i){throw ml(l,i),i}return a instanceof Promise?a.then(e=>(ml(s,e),e)).catch(e=>(ml(l,e),Promise.reject(e))):(ml(s,a),a)};return o[vl]=!0,o[_l]=n,o},y=ct({_p:r,$id:e,$onAction:hl.bind(null,d),$patch:g,$reset:v,$subscribe(t,n={}){const o=hl(p,t,n.detached,()=>s()),s=l.run(()=>zr(()=>r.state.value[e],r=>{("sync"===n.flush?u:c)&&t({storeId:e,type:ul.direct,events:f},r)},wl({},i,n)));return o},$dispose:function(){l.stop(),p=[],d=[],r._s.delete(e)}});r._s.set(e,y);const b=(r._a&&r._a.runWithContext||gl)(()=>r._e.run(()=>(l=te()).run(()=>t({action:_}))));for(const k in b){const t=b[k];if(kt(t)&&!xl(t)||dt(t))s||(h&&kl(t)&&(kt(t)?t.value=h[k]:yl(t,h[k])),r.state.value[e][k]=t);else if("function"==typeof t){const e=_(t,k);b[k]=e,a.actions[k]=t}}return wl(y,b),wl(vt(y),b),Object.defineProperty(y,"$state",{get:()=>r.state.value[e],set:e=>{g(t=>{wl(t,e)})}}),r._p.forEach(e=>{wl(y,l.run(()=>e({store:y,app:r._a,pinia:r,options:a})))}),h&&s&&n.hydrate&&n.hydrate(y.$state,h),c=!0,u=!0,y}
/*! #__NO_SIDE_EFFECTS__ */function Ll(e,t,n){let r;const o="function"==typeof t;function s(n,s){(n=n||(!!(Ro||nn||br)?wr(il,null):null))&&al(n),(n=ll)._s.has(e)||(o?Cl(e,t,r,n):Sl(e,r,n));return n._s.get(e)}return r=o?n:t,s.$id=e,s}function Ol(e){const t=vt(e),n={};for(const r in t){const o=t[r];o.effect?n[r]=Yo({get:()=>e[r],set(t){e[r]=t}}):(kt(o)||dt(o))&&(n[r]=Pt(e,r))}return n}
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const El="undefined"!=typeof document;function Fl(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const Tl=Object.assign;function Pl(e,t){const n={};for(const r in t){const o=t[r];n[r]=$l(o)?o.map(e):e(o)}return n}const Il=()=>{},$l=Array.isArray,Rl=/#/g,Ml=/&/g,Al=/\//g,jl=/=/g,Nl=/\?/g,Wl=/\+/g,Dl=/%5B/g,Vl=/%5D/g,Ul=/%5E/g,Hl=/%60/g,Bl=/%7B/g,Gl=/%7C/g,zl=/%7D/g,Yl=/%20/g;function ql(e){return encodeURI(""+e).replace(Gl,"|").replace(Dl,"[").replace(Vl,"]")}function Kl(e){return ql(e).replace(Wl,"%2B").replace(Yl,"+").replace(Rl,"%23").replace(Ml,"%26").replace(Hl,"`").replace(Bl,"{").replace(zl,"}").replace(Ul,"^")}function Jl(e){return Kl(e).replace(jl,"%3D")}function Zl(e){return null==e?"":function(e){return ql(e).replace(Rl,"%23").replace(Nl,"%3F")}(e).replace(Al,"%2F")}function Xl(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ql=/\/$/;function ea(e,t,n="/"){let r,o={},s="",l="";const a=t.indexOf("#");let i=t.indexOf("?");return a<i&&a>=0&&(i=-1),i>-1&&(r=t.slice(0,i),s=t.slice(i+1,a>-1?a:t.length),o=e(s)),a>-1&&(r=r||t.slice(0,a),l=t.slice(a,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];".."!==o&&"."!==o||r.push("");let s,l,a=n.length-1;for(s=0;s<r.length;s++)if(l=r[s],"."!==l){if(".."!==l)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+r.slice(s).join("/")}(null!=r?r:t,n),{fullPath:r+(s&&"?")+s+l,path:r,query:o,hash:Xl(l)}}function ta(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function na(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ra(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!oa(e[n],t[n]))return!1;return!0}function oa(e,t){return $l(e)?sa(e,t):$l(t)?sa(t,e):e===t}function sa(e,t){return $l(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):1===e.length&&e[0]===t}const la={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var aa,ia,ca,ua;function fa(e){if(!e)if(El){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Ql,"")}(ia=aa||(aa={})).pop="pop",ia.push="push",(ua=ca||(ca={})).back="back",ua.forward="forward",ua.unknown="";const pa=/^[^#]+#/;function da(e,t){return e.replace(pa,"#")+t}const ha=()=>({left:window.scrollX,top:window.scrollY});function ma(e){let t;if("el"in e){const n=e.el,r="string"==typeof n&&n.startsWith("#"),o="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function ga(e,t){return(history.state?history.state.position-t:-1)+e}const va=new Map;function _a(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let t=o.includes(e.slice(s))?e.slice(s).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),ta(n,"")}return ta(n,e)+r+o}function ya(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?ha():null}}function ba(e){const{history:t,location:n}=window,r={value:_a(e,n)},o={value:t.state};function s(r,s,l){const a=e.indexOf("#"),i=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+r:location.protocol+"//"+location.host+e+r;try{t[l?"replaceState":"pushState"](s,"",i),o.value=s}catch(c){n[l?"replace":"assign"](i)}}return o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:function(e,n){const l=Tl({},o.value,t.state,{forward:e,scroll:ha()});s(l.current,l,!0),s(e,Tl({},ya(r.value,e,null),{position:l.position+1},n),!1),r.value=e},replace:function(e,n){s(e,Tl({},t.state,ya(o.value.back,e,o.value.forward,!0),n,{position:o.value.position}),!0),r.value=e}}}function ka(e){const t=ba(e=fa(e)),n=function(e,t,n,r){let o=[],s=[],l=null;const a=({state:s})=>{const a=_a(e,location),i=n.value,c=t.value;let u=0;if(s){if(n.value=a,t.value=s,l&&l===i)return void(l=null);u=c?s.position-c.position:0}else r(a);o.forEach(e=>{e(n.value,i,{delta:u,type:aa.pop,direction:u?u>0?ca.forward:ca.back:ca.unknown})})};function i(){const{history:e}=window;e.state&&e.replaceState(Tl({},e.state,{scroll:ha()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",i,{passive:!0}),{pauseListeners:function(){l=n.value},listen:function(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return s.push(t),t},destroy:function(){for(const e of s)e();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",i)}}}(e,t.state,t.location,t.replace);const r=Tl({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:da.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function wa(e){return"string"==typeof e||"symbol"==typeof e}const xa=Symbol("");var Sa,Ca;function La(e,t){return Tl(new Error,{type:e,[xa]:!0},t)}function Oa(e,t){return e instanceof Error&&xa in e&&(null==t||!!(e.type&t))}(Ca=Sa||(Sa={}))[Ca.aborted=4]="aborted",Ca[Ca.cancelled=8]="cancelled",Ca[Ca.duplicated=16]="duplicated";const Ea="[^/]+?",Fa={sensitive:!1,strict:!1,start:!0,end:!0},Ta=/[.+*?^${}()[\]/\\]/g;function Pa(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Ia(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const e=Pa(r[n],o[n]);if(e)return e;n++}if(1===Math.abs(o.length-r.length)){if($a(r))return 1;if($a(o))return-1}return o.length-r.length}function $a(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ra={type:0,value:""},Ma=/[a-zA-Z0-9_]/;function Aa(e,t,n){const r=function(e,t){const n=Tl({},Fa,t),r=[];let o=n.start?"^":"";const s=[];for(const i of e){const e=i.length?[]:[90];n.strict&&!i.length&&(o+="/");for(let t=0;t<i.length;t++){const r=i[t];let l=40+(n.sensitive?.25:0);if(0===r.type)t||(o+="/"),o+=r.value.replace(Ta,"\\$&"),l+=40;else if(1===r.type){const{value:e,repeatable:n,optional:c,regexp:u}=r;s.push({name:e,repeatable:n,optional:c});const f=u||Ea;if(f!==Ea){l+=10;try{new RegExp(`(${f})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+a.message)}}let p=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(p=c&&i.length<2?`(?:/${p})`:"/"+p),c&&(p+="?"),o+=p,l+=20,c&&(l+=-8),n&&(l+=-20),".*"===f&&(l+=-50)}e.push(l)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,n.sensitive?"":"i");return{re:l,score:r,keys:s,parse:function(e){const t=e.match(l),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",o=s[r-1];n[o.name]=e&&o.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:s,repeatable:l,optional:a}=e,i=s in t?t[s]:"";if($l(i)&&!l)throw new Error(`Provided param "${s}" is an array but it is not repeatable (* or + modifiers)`);const c=$l(i)?i.join("/"):i;if(!c){if(!a)throw new Error(`Missing required param "${s}"`);o.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Ra]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,r=n;const o=[];let s;function l(){s&&o.push(s),s=[]}let a,i=0,c="",u="";function f(){c&&(0===n?s.push({type:0,value:c}):1===n||2===n||3===n?(s.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function p(){c+=a}for(;i<e.length;)if(a=e[i++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&f(),l()):":"===a?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===a?n=2:Ma.test(a)?p():(f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&i--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&i--,u="";break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),l(),o}(e.path),n),o=Tl(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function ja(e,t){const n=[],r=new Map;function o(e,n,r){const a=!r,i=Wa(e);i.aliasOf=r&&r.record;const c=Ha(t,e),u=[i];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Wa(Tl({},i,{components:r?r.record.components:i.components,path:e,aliasOf:r?r.record:i})))}let f,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&r+u)}if(f=Aa(t,n,c),r?r.alias.push(f):(p=p||f,p!==f&&p.alias.push(f),a&&e.name&&!Va(f)&&s(e.name)),Ba(f)&&l(f),i.children){const e=i.children;for(let t=0;t<e.length;t++)o(e[t],f,r&&r.children[t])}r=r||f}return p?()=>{s(p)}:Il}function s(e){if(wa(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(s),t.alias.forEach(s))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(s),e.alias.forEach(s))}}function l(e){const t=function(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Ia(e,t[o])<0?r=o:n=o+1}const o=function(e){let t=e;for(;t=t.parent;)if(Ba(t)&&0===Ia(e,t))return t;return}(e);o&&(r=t.lastIndexOf(o,r-1));return r}(e,n);n.splice(t,0,e),e.record.name&&!Va(e)&&r.set(e.record.name,e)}return t=Ha({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>o(e)),{addRoute:o,resolve:function(e,t){let o,s,l,a={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw La(1,{location:e});l=o.record.name,a=Tl(Na(t.params,o.keys.filter(e=>!e.optional).concat(o.parent?o.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Na(e.params,o.keys.map(e=>e.name))),s=o.stringify(a)}else if(null!=e.path)s=e.path,o=n.find(e=>e.re.test(s)),o&&(a=o.parse(s),l=o.record.name);else{if(o=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!o)throw La(1,{location:e,currentLocation:t});l=o.record.name,a=Tl({},t.params,e.params),s=o.stringify(a)}const i=[];let c=o;for(;c;)i.unshift(c.record),c=c.parent;return{name:l,path:s,params:a,matched:i,meta:Ua(i)}},removeRoute:s,clearRoutes:function(){n.length=0,r.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function Na(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Wa(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Da(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Da(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"==typeof n?n[r]:n;return t}function Va(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ua(e){return e.reduce((e,t)=>Tl(e,t.meta),{})}function Ha(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Ba({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ga(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const e=n[r].replace(Wl," "),o=e.indexOf("="),s=Xl(o<0?e:e.slice(0,o)),l=o<0?null:Xl(e.slice(o+1));if(s in t){let e=t[s];$l(e)||(e=t[s]=[e]),e.push(l)}else t[s]=l}return t}function za(e){let t="";for(let n in e){const r=e[n];if(n=Jl(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}($l(r)?r.map(e=>e&&Kl(e)):[r&&Kl(r)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))})}return t}function Ya(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=$l(r)?r.map(e=>null==e?null:""+e):null==r?r:""+r)}return t}const qa=Symbol(""),Ka=Symbol(""),Ja=Symbol(""),Za=Symbol(""),Xa=Symbol("");function Qa(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function ei(e,t,n,r,o,s=e=>e()){const l=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((a,i)=>{const c=e=>{var s;!1===e?i(La(4,{from:n,to:t})):e instanceof Error?i(e):"string"==typeof(s=e)||s&&"object"==typeof s?i(La(2,{from:t,to:e})):(l&&r.enterCallbacks[o]===l&&"function"==typeof e&&l.push(e),a())},u=s(()=>e.call(r&&r.instances[o],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(e=>i(e))})}function ti(e,t,n,r,o=e=>e()){const s=[];for(const l of e)for(const e in l.components){let a=l.components[e];if("beforeRouteEnter"===t||l.instances[e])if(Fl(a)){const i=(a.__vccOpts||a)[t];i&&s.push(ei(i,n,r,l,e,o))}else{let i=a();s.push(()=>i.then(s=>{if(!s)throw new Error(`Couldn't resolve component "${e}" at "${l.path}"`);const a=(i=s).__esModule||"Module"===i[Symbol.toStringTag]||i.default&&Fl(i.default)?s.default:s;var i;l.mods[e]=s,l.components[e]=a;const c=(a.__vccOpts||a)[t];return c&&ei(c,n,r,l,e,o)()}))}}return s}function ni(e){const t=wr(Ja),n=wr(Za),r=Yo(()=>{const n=Lt(e.to);return t.resolve(n)}),o=Yo(()=>{const{matched:e}=r.value,{length:t}=e,o=e[t-1],s=n.matched;if(!o||!s.length)return-1;const l=s.findIndex(na.bind(null,o));if(l>-1)return l;const a=oi(e[t-2]);return t>1&&oi(o)===a&&s[s.length-1].path!==a?s.findIndex(na.bind(null,e[t-2])):l}),s=Yo(()=>o.value>-1&&function(e,t){for(const n in t){const r=t[n],o=e[n];if("string"==typeof r){if(r!==o)return!1}else if(!$l(o)||o.length!==r.length||r.some((e,t)=>e!==o[t]))return!1}return!0}(n.params,r.value.params)),l=Yo(()=>o.value>-1&&o.value===n.matched.length-1&&ra(n.params,r.value.params));return{route:r,href:Yo(()=>r.value.href),isActive:s,isExactActive:l,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Lt(e.replace)?"replace":"push"](Lt(e.to)).catch(Il);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}}}const ri=Sn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ni,setup(e,{slots:t}){const n=ct(ni(e)),{options:r}=wr(Ja),o=Yo(()=>({[si(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[si(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&(1===(s=t.default(n)).length?s[0]:s);var s;return e.custom?r:qo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}});function oi(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const si=(e,t,n)=>null!=e?e:null!=t?t:n;function li(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const ai=Sn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=wr(Xa),o=Yo(()=>e.route||r.value),s=wr(Ka,0),l=Yo(()=>{let e=Lt(s);const{matched:t}=o.value;let n;for(;(n=t[e])&&!n.components;)e++;return e}),a=Yo(()=>o.value.matched[l.value]);kr(Ka,Yo(()=>l.value+1)),kr(qa,a),kr(Xa,o);const i=wt();return zr(()=>[i.value,a.value,e.name],([e,t,n],[r,o,s])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&na(t,o)&&r||(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const r=o.value,s=e.name,l=a.value,c=l&&l.components[s];if(!c)return li(n.default,{Component:c,route:r});const u=l.props[s],f=u?!0===u?r.params:"function"==typeof u?u(r):u:null,p=qo(c,Tl({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(l.instances[s]=null)},ref:i}));return li(n.default,{Component:p,route:r})||p}}});function ii(e){const t=ja(e.routes,e),n=e.parseQuery||Ga,r=e.stringifyQuery||za,o=e.history,s=Qa(),l=Qa(),a=Qa(),i=xt(la);let c=la;El&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Pl.bind(null,e=>""+e),f=Pl.bind(null,Zl),p=Pl.bind(null,Xl);function d(e,s){if(s=Tl({},s||i.value),"string"==typeof e){const r=ea(n,e,s.path),l=t.resolve({path:r.path},s),a=o.createHref(r.fullPath);return Tl(r,l,{params:p(l.params),hash:Xl(r.hash),redirectedFrom:void 0,href:a})}let l;if(null!=e.path)l=Tl({},e,{path:ea(n,e.path,s.path).path});else{const t=Tl({},e.params);for(const e in t)null==t[e]&&delete t[e];l=Tl({},e,{params:f(t)}),s.params=f(s.params)}const a=t.resolve(l,s),c=e.hash||"";a.params=u(p(a.params));const d=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(r,Tl({},e,{hash:(h=c,ql(h).replace(Bl,"{").replace(zl,"}").replace(Ul,"^")),path:a.path}));var h;const m=o.createHref(d);return Tl({fullPath:d,hash:c,query:r===za?Ya(e.query):e.query||{}},a,{redirectedFrom:void 0,href:m})}function h(e){return"string"==typeof e?ea(n,e,i.value.path):Tl({},e)}function m(e,t){if(c!==e)return La(8,{from:t,to:e})}function g(e){return _(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"==typeof n?n(e):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=h(r):{path:r},r.params={}),Tl({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function _(e,t){const n=c=d(e),o=i.value,s=e.state,l=e.force,a=!0===e.replace,u=v(n);if(u)return _(Tl(h(u),{state:"object"==typeof u?Tl({},s,u.state):s,force:l,replace:a}),t||n);const f=n;let p;return f.redirectedFrom=t,!l&&function(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&na(t.matched[r],n.matched[o])&&ra(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(r,o,n)&&(p=La(16,{to:f,from:o}),P(o,o,!0,!1)),(p?Promise.resolve(p):k(f,o)).catch(e=>Oa(e)?Oa(e,2)?e:T(e):F(e,f,o)).then(e=>{if(e){if(Oa(e,2))return _(Tl({replace:a},h(e.to),{state:"object"==typeof e.to?Tl({},s,e.to.state):s,force:l}),t||f)}else e=x(f,o,!0,a,s);return w(f,o,e),e})}function y(e,t){const n=m(e,t);return n?Promise.reject(n):Promise.resolve()}function b(e){const t=R.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function k(e,t){let n;const[r,o,a]=function(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let l=0;l<s;l++){const s=t.matched[l];s&&(e.matched.find(e=>na(e,s))?r.push(s):n.push(s));const a=e.matched[l];a&&(t.matched.find(e=>na(e,a))||o.push(a))}return[n,r,o]}(e,t);n=ti(r.reverse(),"beforeRouteLeave",e,t);for(const s of r)s.leaveGuards.forEach(r=>{n.push(ei(r,e,t))});const i=y.bind(null,e,t);return n.push(i),A(n).then(()=>{n=[];for(const r of s.list())n.push(ei(r,e,t));return n.push(i),A(n)}).then(()=>{n=ti(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach(r=>{n.push(ei(r,e,t))});return n.push(i),A(n)}).then(()=>{n=[];for(const r of a)if(r.beforeEnter)if($l(r.beforeEnter))for(const o of r.beforeEnter)n.push(ei(o,e,t));else n.push(ei(r.beforeEnter,e,t));return n.push(i),A(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=ti(a,"beforeRouteEnter",e,t,b),n.push(i),A(n))).then(()=>{n=[];for(const r of l.list())n.push(ei(r,e,t));return n.push(i),A(n)}).catch(e=>Oa(e,8)?e:Promise.reject(e))}function w(e,t,n){a.list().forEach(r=>b(()=>r(e,t,n)))}function x(e,t,n,r,s){const l=m(e,t);if(l)return l;const a=t===la,c=El?history.state:{};n&&(r||a?o.replace(e.fullPath,Tl({scroll:a&&c&&c.scroll},s)):o.push(e.fullPath,s)),i.value=e,P(e,t,n,a),T()}let S;function C(){S||(S=o.listen((e,t,n)=>{if(!M.listening)return;const r=d(e),s=v(r);if(s)return void _(Tl(s,{replace:!0,force:!0}),r).catch(Il);c=r;const l=i.value;var a,u;El&&(a=ga(l.fullPath,n.delta),u=ha(),va.set(a,u)),k(r,l).catch(e=>Oa(e,12)?e:Oa(e,2)?(_(Tl(h(e.to),{force:!0}),r).then(e=>{Oa(e,20)&&!n.delta&&n.type===aa.pop&&o.go(-1,!1)}).catch(Il),Promise.reject()):(n.delta&&o.go(-n.delta,!1),F(e,r,l))).then(e=>{(e=e||x(r,l,!1))&&(n.delta&&!Oa(e,8)?o.go(-n.delta,!1):n.type===aa.pop&&Oa(e,20)&&o.go(-1,!1)),w(r,l,e)}).catch(Il)}))}let L,O=Qa(),E=Qa();function F(e,t,n){T(e);const r=E.list();return r.length&&r.forEach(r=>r(e,t,n)),Promise.reject(e)}function T(e){return L||(L=!e,C(),O.list().forEach(([t,n])=>e?n(e):t()),O.reset()),e}function P(t,n,r,o){const{scrollBehavior:s}=e;if(!El||!s)return Promise.resolve();const l=!r&&function(e){const t=va.get(e);return va.delete(e),t}(ga(t.fullPath,0))||(o||!r)&&history.state&&history.state.scroll||null;return Kt().then(()=>s(t,n,l)).then(e=>e&&ma(e)).catch(e=>F(e,t,n))}const I=e=>o.go(e);let $;const R=new Set,M={currentRoute:i,listening:!0,addRoute:function(e,n){let r,o;return wa(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:d,options:e,push:g,replace:function(e){return g(Tl(h(e),{replace:!0}))},go:I,back:()=>I(-1),forward:()=>I(1),beforeEach:s.add,beforeResolve:l.add,afterEach:a.add,onError:E.add,isReady:function(){return L&&i.value!==la?Promise.resolve():new Promise((e,t)=>{O.add([e,t])})},install(e){e.component("RouterLink",ri),e.component("RouterView",ai),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Lt(i)}),El&&!$&&i.value===la&&($=!0,g(o.location).catch(e=>{}));const t={};for(const r in la)Object.defineProperty(t,r,{get:()=>i.value[r],enumerable:!0});e.provide(Ja,this),e.provide(Za,ut(t)),e.provide(Xa,i);const n=e.unmount;R.add(e),e.unmount=function(){R.delete(e),R.size<1&&(c=la,S&&S(),S=null,i.value=la,$=!1,L=!1),n()}}};function A(e){return e.reduce((e,t)=>e.then(()=>b(t)),Promise.resolve())}return M}function ci(){return wr(Ja)}function ui(e){return wr(Za)}
/*!
  * shared v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const fi="undefined"!=typeof window,pi=(e,t=!1)=>t?Symbol.for(e):Symbol(e),di=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),hi=e=>"number"==typeof e&&isFinite(e),mi=e=>"[object RegExp]"===Pi(e),gi=e=>Ii(e)&&0===Object.keys(e).length,vi=Object.assign,_i=Object.create,yi=(e=null)=>_i(e);let bi;const ki=()=>bi||(bi="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:yi());function wi(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const xi=Object.prototype.hasOwnProperty;function Si(e,t){return xi.call(e,t)}const Ci=Array.isArray,Li=e=>"function"==typeof e,Oi=e=>"string"==typeof e,Ei=e=>"boolean"==typeof e,Fi=e=>null!==e&&"object"==typeof e,Ti=Object.prototype.toString,Pi=e=>Ti.call(e),Ii=e=>"[object Object]"===Pi(e);function $i(e,t=""){return e.reduce((e,n,r)=>0===r?e+n:e+t+n,"")}function Ri(e,t){}const Mi=e=>!Fi(e)||Ci(e);function Ai(e,t){if(Mi(e)||Mi(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach(r=>{"__proto__"!==r&&(Fi(e[r])&&!Fi(t[r])&&(t[r]=Array.isArray(e[r])?[]:yi()),Mi(t[r])||Mi(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))})}}
/*!
  * message-compiler v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function ji(e,t,n){return{start:e,end:t}}const Ni=1,Wi=2,Di=3,Vi=4,Ui=5,Hi=6,Bi=7,Gi=8,zi=9,Yi=10,qi=11,Ki=12,Ji=13,Zi=14;function Xi(e,t,n={}){const{domain:r,messages:o,args:s}=n,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=r,l}function Qi(e){throw e}const ec=" ",tc="\n",nc=String.fromCharCode(8232),rc=String.fromCharCode(8233);function oc(e){const t=e;let n=0,r=1,o=1,s=0;const l=e=>"\r"===t[e]&&t[e+1]===tc,a=e=>t[e]===rc,i=e=>t[e]===nc,c=e=>l(e)||(e=>t[e]===tc)(e)||a(e)||i(e),u=e=>l(e)||a(e)||i(e)?tc:t[e];function f(){return s=0,c(n)&&(r++,o=0),l(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>s,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+s),next:f,peek:function(){return l(n+s)&&s++,s++,t[n+s]},reset:function(){n=0,r=1,o=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=n+s;for(;e!==n;)f();s=0}}}const sc=void 0;function lc(e,t={}){const n=!1!==t.location,r=oc(e),o=()=>r.index(),s=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},l=s(),a=o(),i={currentType:13,offset:a,startLoc:l,endLoc:l,lastType:13,lastOffset:a,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},c=()=>i,{onError:u}=t;function f(e,t,r,...o){const s=c();if(t.column+=r,t.offset+=r,u){const r=Xi(e,n?ji(s.startLoc,t):null,{domain:"tokenizer",args:o});u(r)}}function p(e,t,r){e.endLoc=s(),e.currentType=t;const o={type:t};return n&&(o.loc=ji(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const d=e=>p(e,13);function h(e,t){return e.currentChar()===t?(e.next(),t):(f(Ni,s(),0,t),"")}function m(e){let t="";for(;e.currentPeek()===ec||e.currentPeek()===tc;)t+=e.currentPeek(),e.peek();return t}function g(e){const t=m(e);return e.skipToPeek(),t}function v(e){if(e===sc)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function _(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=function(e){if(e===sc)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function y(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function b(e,t=!0){const n=(t=!1,r="")=>{const o=e.currentPeek();return"{"===o?t:"@"!==o&&o?"|"===o?!(r===ec||r===tc):o===ec?(e.peek(),n(!0,ec)):o!==tc||(e.peek(),n(!0,tc)):t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===sc?sc:t(n)?(e.next(),n):null}function w(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function x(e){return k(e,w)}function S(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function C(e){return k(e,S)}function L(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function O(e){return k(e,L)}function E(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function F(e){return k(e,E)}function T(e){let t="",n="";for(;t=O(e);)n+=t;return n}function P(e){return"'"!==e&&e!==tc}function I(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return $(e,t,4);case"U":return $(e,t,6);default:return f(Vi,s(),0,t),""}}function $(e,t,n){h(e,t);let r="";for(let o=0;o<n;o++){const n=F(e);if(!n){f(Ui,s(),0,`\\${t}${r}${e.currentChar()}`);break}r+=n}return`\\${t}${r}`}function R(e){return"{"!==e&&"}"!==e&&e!==ec&&e!==tc}function M(e){g(e);const t=h(e,"|");return g(e),t}function A(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&f(zi,s(),0),e.next(),n=p(t,2,"{"),g(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&f(Gi,s(),0),e.next(),n=p(t,3,"}"),t.braceNest--,t.braceNest>0&&g(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&f(Bi,s(),0),n=j(e,t)||d(t),t.braceNest=0,n;default:{let r=!0,o=!0,l=!0;if(y(e))return t.braceNest>0&&f(Bi,s(),0),n=p(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return f(Bi,s(),0),t.braceNest=0,N(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=v(e.currentPeek());return e.resetPeek(),r}(e,t))return n=p(t,4,function(e){g(e);let t="",n="";for(;t=C(e);)n+=t;return e.currentChar()===sc&&f(Bi,s(),0),n}(e)),g(e),n;if(o=_(e,t))return n=p(t,5,function(e){g(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${T(e)}`):t+=T(e),e.currentChar()===sc&&f(Bi,s(),0),t}(e)),g(e),n;if(l=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=p(t,6,function(e){g(e),h(e,"'");let t="",n="";for(;t=k(e,P);)n+="\\"===t?I(e):t;const r=e.currentChar();return r===tc||r===sc?(f(Di,s(),0),r===tc&&(e.next(),h(e,"'")),n):(h(e,"'"),n)}(e)),g(e),n;if(!r&&!o&&!l)return n=p(t,12,function(e){g(e);let t="",n="";for(;t=k(e,R);)n+=t;return n}(e)),f(Wi,s(),0,n.value),g(e),n;break}}return n}function j(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||o!==tc&&o!==ec||f(Yi,s(),0),o){case"@":return e.next(),r=p(t,7,"@"),t.inLinked=!0,r;case".":return g(e),e.next(),p(t,8,".");case":":return g(e),e.next(),p(t,9,":");default:return y(e)?(r=p(t,1,M(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(g(e),j(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const r=v(e.currentPeek());return e.resetPeek(),r}(e,t)?(g(e),p(t,11,function(e){let t="",n="";for(;t=x(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?v(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===ec||!t)&&(t===tc?(e.peek(),r()):b(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(g(e),"{"===o?A(e,t)||r:p(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===ec?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&f(Yi,s(),0),t.braceNest=0,t.inLinked=!1,N(e,t))}}function N(e,t){let n={type:13};if(t.braceNest>0)return A(e,t)||d(t);if(t.inLinked)return j(e,t)||d(t);switch(e.currentChar()){case"{":return A(e,t)||d(t);case"}":return f(Hi,s(),0),e.next(),p(t,3,"}");case"@":return j(e,t)||d(t);default:if(y(e))return n=p(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(b(e))return p(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===ec||n===tc)if(b(e))t+=n,e.next();else{if(y(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:l}=i;return i.lastType=e,i.lastOffset=t,i.lastStartLoc=n,i.lastEndLoc=l,i.offset=o(),i.startLoc=s(),r.currentChar()===sc?p(i,13):N(r,i)},currentOffset:o,currentPosition:s,context:c}}const ac=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function ic(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function cc(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,r,o,s,...l){const a=e.currentPosition();if(a.offset+=s,a.column+=s,n){const e=Xi(r,t?ji(o,a):null,{domain:"parser",args:l});n(e)}}function o(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function s(e,n,r,o){t&&(e.end=n,e.loc&&(e.loc.end=r))}function l(e,t){const n=e.context(),r=o(3,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,a=o(5,r,l);return a.index=parseInt(t,10),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function i(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,a=o(4,r,l);return a.key=t,e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function c(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,a=o(9,r,l);return a.value=t.replace(ac,ic),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function u(e){const t=e.context(),n=o(6,t.offset,t.startLoc);let l=e.nextToken();if(8===l.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:l,lastStartLoc:a}=n,i=o(8,l,a);return 11!==t.type?(r(e,Ki,n.lastStartLoc,0),i.value="",s(i,l,a),{nextConsumeToken:t,node:i}):(null==t.value&&r(e,Zi,n.lastStartLoc,0,uc(t)),i.value=t.value||"",s(i,e.currentOffset(),e.currentPosition()),{node:i})}(e);n.modifier=t.node,l=t.nextConsumeToken||e.nextToken()}switch(9!==l.type&&r(e,Zi,t.lastStartLoc,0,uc(l)),l=e.nextToken(),2===l.type&&(l=e.nextToken()),l.type){case 10:null==l.value&&r(e,Zi,t.lastStartLoc,0,uc(l)),n.key=function(e,t){const n=e.context(),r=o(7,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}(e,l.value||"");break;case 4:null==l.value&&r(e,Zi,t.lastStartLoc,0,uc(l)),n.key=i(e,l.value||"");break;case 5:null==l.value&&r(e,Zi,t.lastStartLoc,0,uc(l)),n.key=a(e,l.value||"");break;case 6:null==l.value&&r(e,Zi,t.lastStartLoc,0,uc(l)),n.key=c(e,l.value||"");break;default:{r(e,Ji,t.lastStartLoc,0);const a=e.context(),i=o(7,a.offset,a.startLoc);return i.value="",s(i,a.offset,a.startLoc),n.key=i,s(n,a.offset,a.startLoc),{nextConsumeToken:l,node:n}}}return s(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let f=null;do{const o=f||e.nextToken();switch(f=null,o.type){case 0:null==o.value&&r(e,Zi,t.lastStartLoc,0,uc(o)),n.items.push(l(e,o.value||""));break;case 5:null==o.value&&r(e,Zi,t.lastStartLoc,0,uc(o)),n.items.push(a(e,o.value||""));break;case 4:null==o.value&&r(e,Zi,t.lastStartLoc,0,uc(o)),n.items.push(i(e,o.value||""));break;case 6:null==o.value&&r(e,Zi,t.lastStartLoc,0,uc(o)),n.items.push(c(e,o.value||""));break;case 7:{const t=u(e);n.items.push(t.node),f=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return s(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function p(e){const t=e.context(),{offset:n,startLoc:l}=t,a=f(e);return 13===t.currentType?a:function(e,t,n,l){const a=e.context();let i=0===l.items.length;const c=o(1,t,n);c.cases=[],c.cases.push(l);do{const t=f(e);i||(i=0===t.items.length),c.cases.push(t)}while(13!==a.currentType);return i&&r(e,qi,n,0),s(c,e.currentOffset(),e.currentPosition()),c}(e,n,l,a)}return{parse:function(n){const l=lc(n,vi({},e)),a=l.context(),i=o(0,a.offset,a.startLoc);return t&&i.loc&&(i.loc.source=n),i.body=p(l),e.onCacheKey&&(i.cacheKey=e.onCacheKey(n)),13!==a.currentType&&r(l,Zi,a.lastStartLoc,0,n[a.offset]||""),s(i,l.currentOffset(),l.currentPosition()),i}}}function uc(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function fc(e,t){for(let n=0;n<e.length;n++)pc(e[n],t)}function pc(e,t){switch(e.type){case 1:fc(e.cases,t),t.helper("plural");break;case 2:fc(e.items,t);break;case 6:pc(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function dc(e,t={}){const n=function(e){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&pc(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function hc(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=$i(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function mc(e){switch(e.t=e.type,e.type){case 0:{const t=e;mc(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)mc(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)mc(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;mc(t.key),t.k=t.key,delete t.key,t.modifier&&(mc(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function gc(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?gc(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(gc(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let s=0;s<o&&(gc(e,t.items[s]),s!==o-1);s++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),gc(e,t.key),t.modifier?(e.push(", "),gc(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function vc(e,t={}){const n=vi({},t),r=!!n.jit,o=!!n.minify,s=null==n.optimize||n.optimize,l=cc(n).parse(e);return r?(s&&function(e){const t=e.body;2===t.type?hc(t):t.cases.forEach(e=>hc(e))}(l),o&&mc(l),{ast:l,code:""}):(dc(l,n),((e,t={})=>{const n=Oi(t.mode)?t.mode:"normal",r=Oi(t.filename)?t.filename:"message.intl";t.sourceMap;const o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,l=e.helpers||[],a=function(e,t){const{filename:n,breakLineCode:r,needIndent:o}=t,s=!1!==t.location,l={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:o,indentLevel:0};function a(e,t){l.code+=e}function i(e,t=!0){const n=t?r:"";a(o?n+"  ".repeat(e):n)}return s&&e.loc&&(l.source=e.loc.source),{context:()=>l,push:a,indent:function(e=!0){const t=++l.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--l.indentLevel;e&&i(t)},newline:function(){i(l.indentLevel)},helper:e=>`_${e}`,needIndent:()=>l.needIndent}}(e,{filename:r,breakLineCode:o,needIndent:s});a.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(s),l.length>0&&(a.push(`const { ${$i(l.map(e=>`${e}: _${e}`),", ")} } = ctx`),a.newline()),a.push("return "),gc(a,e),a.deindent(s),a.push("}"),delete e.helpers;const{code:i,map:c}=a.context();return{ast:e,code:i,map:c?c.toJSON():void 0}})(l,n))}
/*!
  * core-base v11.1.7
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function _c(e){return Fi(e)&&0===Sc(e)&&(Si(e,"b")||Si(e,"body"))}const yc=["b","body"];const bc=["c","cases"];const kc=["s","static"];const wc=["i","items"];const xc=["t","type"];function Sc(e){return Fc(e,xc)}const Cc=["v","value"];function Lc(e,t){const n=Fc(e,Cc);if(null!=n)return n;throw Pc(t)}const Oc=["m","modifier"];const Ec=["k","key"];function Fc(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(Si(e,n)&&null!=e[n])return e[n]}return n}const Tc=[...yc,...bc,...kc,...wc,...Ec,...Oc,...Cc,...xc];function Pc(e){return new Error(`unhandled node type: ${e}`)}function Ic(e){return t=>function(e,t){const n=(r=t,Fc(r,yc));var r;if(null==n)throw Pc(0);if(1===Sc(n)){const t=function(e){return Fc(e,bc,[])}(n);return e.plural(t.reduce((t,n)=>[...t,$c(e,n)],[]))}return $c(e,n)}(t,e)}function $c(e,t){const n=function(e){return Fc(e,kc)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return Fc(e,wc,[])}(t).reduce((t,n)=>[...t,Rc(e,n)],[]);return e.normalize(n)}}function Rc(e,t){const n=Sc(t);switch(n){case 3:case 9:case 7:case 8:return Lc(t,n);case 4:{const r=t;if(Si(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(Si(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Pc(n)}case 5:{const r=t;if(Si(r,"i")&&hi(r.i))return e.interpolate(e.list(r.i));if(Si(r,"index")&&hi(r.index))return e.interpolate(e.list(r.index));throw Pc(n)}case 6:{const n=t,r=function(e){return Fc(e,Oc)}(n),o=function(e){const t=Fc(e,Ec);if(t)return t;throw Pc(6)}(n);return e.linked(Rc(e,o),r?Rc(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const Mc=e=>e;let Ac=yi();let jc=null;const Nc=Wc("function:translate");function Wc(e){return t=>jc&&jc.emit(e,t)}const Dc=17,Vc=18,Uc=19,Hc=21,Bc=22,Gc=23;function zc(e){return Xi(e,null,void 0)}function Yc(e,t){return null!=t.locale?Kc(t.locale):Kc(e.locale)}let qc;function Kc(e){if(Oi(e))return e;if(Li(e)){if(e.resolvedOnce&&null!=qc)return qc;if("Function"===e.constructor.name){const n=e();if(Fi(t=n)&&Li(t.then)&&Li(t.catch))throw zc(Hc);return qc=n}throw zc(Bc)}throw zc(Gc);var t}function Jc(e,t,n){return[...new Set([n,...Ci(t)?t:Fi(t)?Object.keys(t):Oi(t)?[t]:[n]])]}function Zc(e,t,n){const r=Oi(n)?n:au,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let s=o.__localeChainCache.get(r);if(!s){s=[];let e=[n];for(;Ci(e);)e=Xc(s,e,t);const l=Ci(t)||!Ii(t)?t:t.default?t.default:null;e=Oi(l)?[l]:l,Ci(e)&&Xc(s,e,!1),o.__localeChainCache.set(r,s)}return s}function Xc(e,t,n){let r=!0;for(let o=0;o<t.length&&Ei(r);o++){const s=t[o];Oi(s)&&(r=Qc(e,t[o],n))}return r}function Qc(e,t,n){let r;const o=t.split("-");do{r=eu(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function eu(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(Ci(n)||Ii(n))&&n[o]&&(r=n[o])}return r}const tu=[];tu[0]={w:[0],i:[3,0],"[":[4],o:[7]},tu[1]={w:[1],".":[2],"[":[4],o:[7]},tu[2]={w:[2],i:[3,0],0:[3,0]},tu[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},tu[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},tu[5]={"'":[4,0],o:8,l:[5,0]},tu[6]={'"':[4,0],o:8,l:[6,0]};const nu=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ru(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function ou(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,nu.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const su=new Map;function lu(e,t){return Fi(e)?e[t]:null}const au="en-US",iu=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let cu,uu,fu;let pu=null;const du=()=>pu;let hu=null;const mu=e=>{hu=e};let gu=0;function vu(e={}){const t=Li(e.onWarn)?e.onWarn:Ri,n=Oi(e.version)?e.version:"11.1.7",r=Oi(e.locale)||Li(e.locale)?e.locale:au,o=Li(r)?au:r,s=Ci(e.fallbackLocale)||Ii(e.fallbackLocale)||Oi(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o,l=Ii(e.messages)?e.messages:_u(o),a=Ii(e.datetimeFormats)?e.datetimeFormats:_u(o),i=Ii(e.numberFormats)?e.numberFormats:_u(o),c=vi(yi(),e.modifiers,{upper:(e,t)=>"text"===t&&Oi(e)?e.toUpperCase():"vnode"===t&&Fi(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&Oi(e)?e.toLowerCase():"vnode"===t&&Fi(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&Oi(e)?iu(e):"vnode"===t&&Fi(e)&&"__v_isVNode"in e?iu(e.children):e}),u=e.pluralRules||yi(),f=Li(e.missing)?e.missing:null,p=!Ei(e.missingWarn)&&!mi(e.missingWarn)||e.missingWarn,d=!Ei(e.fallbackWarn)&&!mi(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,m=!!e.unresolving,g=Li(e.postTranslation)?e.postTranslation:null,v=Ii(e.processor)?e.processor:null,_=!Ei(e.warnHtmlMessage)||e.warnHtmlMessage,y=!!e.escapeParameter,b=Li(e.messageCompiler)?e.messageCompiler:cu,k=Li(e.messageResolver)?e.messageResolver:uu||lu,w=Li(e.localeFallbacker)?e.localeFallbacker:fu||Jc,x=Fi(e.fallbackContext)?e.fallbackContext:void 0,S=e,C=Fi(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,L=Fi(S.__numberFormatters)?S.__numberFormatters:new Map,O=Fi(S.__meta)?S.__meta:{};gu++;const E={version:n,cid:gu,locale:r,fallbackLocale:s,messages:l,modifiers:c,pluralRules:u,missing:f,missingWarn:p,fallbackWarn:d,fallbackFormat:h,unresolving:m,postTranslation:g,processor:v,warnHtmlMessage:_,escapeParameter:y,messageCompiler:b,messageResolver:k,localeFallbacker:w,fallbackContext:x,onWarn:t,__meta:O};return E.datetimeFormats=a,E.numberFormats=i,E.__datetimeFormatters=C,E.__numberFormatters=L,__INTLIFY_PROD_DEVTOOLS__&&function(e,t,n){jc&&jc.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}(E,n,O),E}const _u=e=>({[e]:yi()});function yu(e,t,n,r,o){const{missing:s,onWarn:l}=e;if(null!==s){const r=s(e,n,t,o);return Oi(r)?r:t}return t}function bu(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function ku(e,t){return e!==t&&e.split("-")[0]===t.split("-")[0]}function wu(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let r=n+1;r<t.length;r++)if(ku(e,t[r]))return!0;return!1}function xu(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o,onWarn:s,localeFallbacker:l}=e,{__datetimeFormatters:a}=e,[i,c,u,f]=Cu(...t);Ei(u.missingWarn)?u.missingWarn:e.missingWarn;Ei(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,d=Yc(e,u),h=l(e,o,d);if(!Oi(i)||""===i)return new Intl.DateTimeFormat(d,f).format(c);let m,g={},v=null;for(let b=0;b<h.length&&(m=h[b],g=n[m]||{},v=g[i],!Ii(v));b++)yu(e,i,m,0,"datetime format");if(!Ii(v)||!Oi(m))return r?-1:i;let _=`${m}__${i}`;gi(f)||(_=`${_}__${JSON.stringify(f)}`);let y=a.get(_);return y||(y=new Intl.DateTimeFormat(m,vi({},v,f)),a.set(_,y)),p?y.formatToParts(c):y.format(c)}const Su=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Cu(...e){const[t,n,r,o]=e,s=yi();let l,a=yi();if(Oi(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw zc(Uc);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(n);try{l.toISOString()}catch{throw zc(Uc)}}else if("[object Date]"===Pi(t)){if(isNaN(t.getTime()))throw zc(Vc);l=t}else{if(!hi(t))throw zc(Dc);l=t}return Oi(n)?s.key=n:Ii(n)&&Object.keys(n).forEach(e=>{Su.includes(e)?a[e]=n[e]:s[e]=n[e]}),Oi(r)?s.locale=r:Ii(r)&&(a=r),Ii(o)&&(a=o),[s.key||"",l,s,a]}function Lu(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function Ou(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o,onWarn:s,localeFallbacker:l}=e,{__numberFormatters:a}=e,[i,c,u,f]=Fu(...t);Ei(u.missingWarn)?u.missingWarn:e.missingWarn;Ei(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const p=!!u.part,d=Yc(e,u),h=l(e,o,d);if(!Oi(i)||""===i)return new Intl.NumberFormat(d,f).format(c);let m,g={},v=null;for(let b=0;b<h.length&&(m=h[b],g=n[m]||{},v=g[i],!Ii(v));b++)yu(e,i,m,0,"number format");if(!Ii(v)||!Oi(m))return r?-1:i;let _=`${m}__${i}`;gi(f)||(_=`${_}__${JSON.stringify(f)}`);let y=a.get(_);return y||(y=new Intl.NumberFormat(m,vi({},v,f)),a.set(_,y)),p?y.formatToParts(c):y.format(c)}const Eu=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Fu(...e){const[t,n,r,o]=e,s=yi();let l=yi();if(!hi(t))throw zc(Dc);const a=t;return Oi(n)?s.key=n:Ii(n)&&Object.keys(n).forEach(e=>{Eu.includes(e)?l[e]=n[e]:s[e]=n[e]}),Oi(r)?s.locale=r:Ii(r)&&(l=r),Ii(o)&&(l=o),[s.key||"",a,s,l]}function Tu(e,t,n){const r=e;for(const o in n){const e=`${t}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const Pu=e=>e,Iu=e=>"",$u=e=>0===e.length?"":$i(e),Ru=e=>null==e?"":Ci(e)||Ii(e)&&e.toString===Ti?JSON.stringify(e,null,2):String(e);function Mu(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Au(e={}){const t=e.locale,n=function(e){const t=hi(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(hi(e.named.count)||hi(e.named.n))?hi(e.named.count)?e.named.count:hi(e.named.n)?e.named.n:t:t}(e),r=Fi(e.pluralRules)&&Oi(t)&&Li(e.pluralRules[t])?e.pluralRules[t]:Mu,o=Fi(e.pluralRules)&&Oi(t)&&Li(e.pluralRules[t])?Mu:void 0,s=e.list||[],l=e.named||yi();hi(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,l);function a(t,n){const r=Li(e.messages)?e.messages(t,!!n):!!Fi(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):Iu)}const i=Ii(e.processor)&&Li(e.processor.normalize)?e.processor.normalize:$u,c=Ii(e.processor)&&Li(e.processor.interpolate)?e.processor.interpolate:Ru,u={list:e=>s[e],named:e=>l[e],plural:e=>e[r(n,e.length,o)],linked:(t,...n)=>{const[r,o]=n;let s="text",l="";1===n.length?Fi(r)?(l=r.modifier||l,s=r.type||s):Oi(r)&&(l=r||l):2===n.length&&(Oi(r)&&(l=r||l),Oi(o)&&(s=o||s));const i=a(t,!0)(u),c="vnode"===s&&Ci(i)&&l?i[0]:i;return l?(f=l,e.modifiers?e.modifiers[f]:Pu)(c,s):c;var f},message:a,type:Ii(e.processor)&&Oi(e.processor.type)?e.processor.type:"text",interpolate:c,normalize:i,values:vi(yi(),s,l)};return u}const ju=()=>"",Nu=e=>Li(e);function Wu(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:o,messageCompiler:s,fallbackLocale:l,messages:a}=e,[i,c]=Uu(...t),u=Ei(c.missingWarn)?c.missingWarn:e.missingWarn,f=Ei(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,p=Ei(c.escapeParameter)?c.escapeParameter:e.escapeParameter,d=!!c.resolvedMessage,h=Oi(c.default)||Ei(c.default)?Ei(c.default)?s?i:()=>i:c.default:n?s?i:()=>i:null,m=n||null!=h&&(Oi(h)||Li(h)),g=Yc(e,c);p&&function(e){Ci(e.list)?e.list=e.list.map(e=>Oi(e)?wi(e):e):Fi(e.named)&&Object.keys(e.named).forEach(t=>{Oi(e.named[t])&&(e.named[t]=wi(e.named[t]))})}(c);let[v,_,y]=d?[i,g,a[g]||yi()]:Du(e,i,g,l,f,u),b=v,k=i;if(d||Oi(b)||_c(b)||Nu(b)||m&&(b=h,k=b),!(d||(Oi(b)||_c(b)||Nu(b))&&Oi(_)))return o?-1:i;let w=!1;const x=Nu(b)?b:Vu(e,i,_,b,k,()=>{w=!0});if(w)return b;const S=function(e,t,n,r){const{modifiers:o,pluralRules:s,messageResolver:l,fallbackLocale:a,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,f=(r,o)=>{let s=l(n,r);if(null==s&&(u||o)){const[,,n]=Du(u||e,r,t,a,i,c);s=l(n,r)}if(Oi(s)||_c(s)){let n=!1;const o=Vu(e,r,t,s,r,()=>{n=!0});return n?ju:o}return Nu(s)?s:ju},p={locale:t,modifiers:o,pluralRules:s,messages:f};e.processor&&(p.processor=e.processor);r.list&&(p.list=r.list);r.named&&(p.named=r.named);hi(r.plural)&&(p.pluralIndex=r.plural);return p}(e,_,y,c),C=function(e,t,n){const r=t(n);return r}(0,x,Au(S)),L=r?r(C,i):C;if(__INTLIFY_PROD_DEVTOOLS__){const t={timestamp:Date.now(),key:Oi(i)?i:Nu(b)?b.key:"",locale:_||(Nu(b)?b.locale:""),format:Oi(b)?b:Nu(b)?b.source:"",message:L};t.meta=vi({},e.__meta,du()||{}),Nc(t)}return L}function Du(e,t,n,r,o,s){const{messages:l,onWarn:a,messageResolver:i,localeFallbacker:c}=e,u=c(e,r,n);let f,p=yi(),d=null;for(let h=0;h<u.length&&(f=u[h],p=l[f]||yi(),null===(d=i(p,t))&&(d=p[t]),!(Oi(d)||_c(d)||Nu(d)));h++)if(!wu(f,u)){const n=yu(e,t,f,0,"translate");n!==t&&(d=n)}return[d,f,p]}function Vu(e,t,n,r,o,s){const{messageCompiler:l,warnHtmlMessage:a}=e;if(Nu(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==l){const e=()=>r;return e.locale=n,e.key=t,e}const i=l(r,function(e,t,n,r,o,s){return{locale:t,key:n,warnHtmlMessage:o,onError:e=>{throw s&&s(e),e},onCacheKey:e=>((e,t,n)=>di({l:e,k:t,s:n}))(t,n,e)}}(0,n,o,0,a,s));return i.locale=n,i.key=t,i.source=r,i}function Uu(...e){const[t,n,r]=e,o=yi();if(!(Oi(t)||hi(t)||Nu(t)||_c(t)))throw zc(Dc);const s=hi(t)?String(t):(Nu(t),t);return hi(n)?o.plural=n:Oi(n)?o.default=n:Ii(n)&&!gi(n)?o.named=n:Ci(n)&&(o.list=n),hi(r)?o.plural=r:Oi(r)?o.default=r:Ii(r)&&vi(o,r),[s,o]}"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(ki().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(ki().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);const Hu=24,Bu=25,Gu=26,zu=27,Yu=28,qu=29,Ku=31,Ju=32;function Zu(e,...t){return Xi(e,null,void 0)}const Xu=pi("__translateVNode"),Qu=pi("__datetimeParts"),ef=pi("__numberParts"),tf=pi("__setPluralRules"),nf=pi("__injectWithOption"),rf=pi("__dispose");function of(e){if(!Fi(e))return e;if(_c(e))return e;for(const t in e)if(Si(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let o=e,s=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in o||(o[n[e]]=yi()),!Fi(o[n[e]])){s=!0;break}o=o[n[e]]}if(s||(_c(o)?Tc.includes(n[r])||delete e[t]:(o[n[r]]=e[t],delete e[t])),!_c(o)){const e=o[n[r]];Fi(e)&&of(e)}}else Fi(e[t])&&of(e[t]);return e}function sf(e,t){const{messages:n,__i18n:r,messageResolver:o,flatJson:s}=t,l=Ii(n)?n:Ci(r)?yi():{[e]:yi()};if(Ci(r)&&r.forEach(e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(l[t]=l[t]||yi(),Ai(n,l[t])):Ai(n,l)}else Oi(e)&&Ai(JSON.parse(e),l)}),null==o&&s)for(const a in l)Si(l,a)&&of(l[a]);return l}function lf(e){return e.type}function af(e,t,n){let r=Fi(t.messages)?t.messages:yi();"__i18nGlobal"in n&&(r=sf(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const o=Object.keys(r);if(o.length&&o.forEach(t=>{e.mergeLocaleMessage(t,r[t])}),Fi(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach(n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])})}if(Fi(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach(n=>{e.mergeNumberFormat(n,t.numberFormats[n])})}}function cf(e){return xo(lo,null,e,0)}const uf=()=>[],ff=()=>!1;let pf=0;function df(e){return(t,n,r,o)=>e(n,r,Mo()||void 0,o)}function hf(e={}){const{__root:t,__injectWithOption:n}=e,r=void 0===t,o=e.flatJson,s=fi?wt:xt;let l=!Ei(e.inheritLocale)||e.inheritLocale;const a=s(t&&l?t.locale.value:Oi(e.locale)?e.locale:au),i=s(t&&l?t.fallbackLocale.value:Oi(e.fallbackLocale)||Ci(e.fallbackLocale)||Ii(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a.value),c=s(sf(a.value,e)),u=s(Ii(e.datetimeFormats)?e.datetimeFormats:{[a.value]:{}}),f=s(Ii(e.numberFormats)?e.numberFormats:{[a.value]:{}});let p=t?t.missingWarn:!Ei(e.missingWarn)&&!mi(e.missingWarn)||e.missingWarn,d=t?t.fallbackWarn:!Ei(e.fallbackWarn)&&!mi(e.fallbackWarn)||e.fallbackWarn,h=t?t.fallbackRoot:!Ei(e.fallbackRoot)||e.fallbackRoot,m=!!e.fallbackFormat,g=Li(e.missing)?e.missing:null,v=Li(e.missing)?df(e.missing):null,_=Li(e.postTranslation)?e.postTranslation:null,y=t?t.warnHtmlMessage:!Ei(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter;const k=t?t.modifiers:Ii(e.modifiers)?e.modifiers:{};let w,x=e.pluralRules||t&&t.pluralRules;w=(()=>{r&&mu(null);const t={version:"11.1.7",locale:a.value,fallbackLocale:i.value,messages:c.value,modifiers:k,pluralRules:x,missing:null===v?void 0:v,missingWarn:p,fallbackWarn:d,fallbackFormat:m,unresolving:!0,postTranslation:null===_?void 0:_,warnHtmlMessage:y,escapeParameter:b,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=u.value,t.numberFormats=f.value,t.__datetimeFormatters=Ii(w)?w.__datetimeFormatters:void 0,t.__numberFormatters=Ii(w)?w.__numberFormatters:void 0;const n=vu(t);return r&&mu(n),n})(),bu(w,a.value,i.value);const S=Yo({get:()=>a.value,set:e=>{w.locale=e,a.value=e}}),C=Yo({get:()=>i.value,set:e=>{w.fallbackLocale=e,i.value=e,bu(w,a.value,e)}}),L=Yo(()=>c.value),O=Yo(()=>u.value),E=Yo(()=>f.value);const F=(e,n,o,s,l,p)=>{let d;a.value,i.value,c.value,u.value,f.value;try{__INTLIFY_PROD_DEVTOOLS__,r||(w.fallbackContext=t?hu:void 0),d=e(w)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(w.fallbackContext=void 0)}if("translate exists"!==o&&hi(d)&&-1===d||"translate exists"===o&&!d){const[e,r]=n();return t&&h?s(t):l(e)}if(p(d))return d;throw Zu(Hu)};function T(...e){return F(t=>Reflect.apply(Wu,null,[t,...e]),()=>Uu(...e),"translate",t=>Reflect.apply(t.t,t,[...e]),e=>e,e=>Oi(e))}const P={normalize:function(e){return e.map(e=>Oi(e)||hi(e)||Ei(e)?cf(String(e)):e)},interpolate:e=>e,type:"vnode"};function I(e){return c.value[e]||{}}pf++,t&&fi&&(zr(t.locale,e=>{l&&(a.value=e,w.locale=e,bu(w,a.value,i.value))}),zr(t.fallbackLocale,e=>{l&&(i.value=e,w.fallbackLocale=e,bu(w,a.value,i.value))}));const $={id:pf,locale:S,fallbackLocale:C,get inheritLocale(){return l},set inheritLocale(e){l=e,e&&t&&(a.value=t.locale.value,i.value=t.fallbackLocale.value,bu(w,a.value,i.value))},get availableLocales(){return Object.keys(c.value).sort()},messages:L,get modifiers(){return k},get pluralRules(){return x||{}},get isGlobal(){return r},get missingWarn(){return p},set missingWarn(e){p=e,w.missingWarn=p},get fallbackWarn(){return d},set fallbackWarn(e){d=e,w.fallbackWarn=d},get fallbackRoot(){return h},set fallbackRoot(e){h=e},get fallbackFormat(){return m},set fallbackFormat(e){m=e,w.fallbackFormat=m},get warnHtmlMessage(){return y},set warnHtmlMessage(e){y=e,w.warnHtmlMessage=e},get escapeParameter(){return b},set escapeParameter(e){b=e,w.escapeParameter=e},t:T,getLocaleMessage:I,setLocaleMessage:function(e,t){if(o){const n={[e]:t};for(const e in n)Si(n,e)&&of(n[e]);t=n[e]}c.value[e]=t,w.messages=c.value},mergeLocaleMessage:function(e,t){c.value[e]=c.value[e]||{};const n={[e]:t};if(o)for(const r in n)Si(n,r)&&of(n[r]);Ai(t=n[e],c.value[e]),w.messages=c.value},getPostTranslationHandler:function(){return Li(_)?_:null},setPostTranslationHandler:function(e){_=e,w.postTranslation=e},getMissingHandler:function(){return g},setMissingHandler:function(e){null!==e&&(v=df(e)),g=e,w.missing=v},[tf]:function(e){x=e,w.pluralRules=x}};return $.datetimeFormats=O,$.numberFormats=E,$.rt=function(...e){const[t,n,r]=e;if(r&&!Fi(r))throw Zu(Bu);return T(t,n,vi({resolvedMessage:!0},r||{}))},$.te=function(e,t){return F(()=>{if(!e)return!1;const n=I(Oi(t)?t:a.value),r=w.messageResolver(n,e);return _c(r)||Nu(r)||Oi(r)},()=>[e],"translate exists",n=>Reflect.apply(n.te,n,[e,t]),ff,e=>Ei(e))},$.tm=function(e){const n=function(e){let t=null;const n=Zc(w,i.value,a.value);for(let r=0;r<n.length;r++){const o=c.value[n[r]]||{},s=w.messageResolver(o,e);if(null!=s){t=s;break}}return t}(e);return null!=n?n:t&&t.tm(e)||{}},$.d=function(...e){return F(t=>Reflect.apply(xu,null,[t,...e]),()=>Cu(...e),"datetime format",t=>Reflect.apply(t.d,t,[...e]),()=>"",e=>Oi(e)||Ci(e))},$.n=function(...e){return F(t=>Reflect.apply(Ou,null,[t,...e]),()=>Fu(...e),"number format",t=>Reflect.apply(t.n,t,[...e]),()=>"",e=>Oi(e)||Ci(e))},$.getDateTimeFormat=function(e){return u.value[e]||{}},$.setDateTimeFormat=function(e,t){u.value[e]=t,w.datetimeFormats=u.value,Lu(w,e,t)},$.mergeDateTimeFormat=function(e,t){u.value[e]=vi(u.value[e]||{},t),w.datetimeFormats=u.value,Lu(w,e,t)},$.getNumberFormat=function(e){return f.value[e]||{}},$.setNumberFormat=function(e,t){f.value[e]=t,w.numberFormats=f.value,Tu(w,e,t)},$.mergeNumberFormat=function(e,t){f.value[e]=vi(f.value[e]||{},t),w.numberFormats=f.value,Tu(w,e,t)},$[nf]=n,$[Xu]=function(...e){return F(t=>{let n;const r=t;try{r.processor=P,n=Reflect.apply(Wu,null,[r,...e])}finally{r.processor=null}return n},()=>Uu(...e),"translate",t=>t[Xu](...e),e=>[cf(e)],e=>Ci(e))},$[Qu]=function(...e){return F(t=>Reflect.apply(xu,null,[t,...e]),()=>Cu(...e),"datetime format",t=>t[Qu](...e),uf,e=>Oi(e)||Ci(e))},$[ef]=function(...e){return F(t=>Reflect.apply(Ou,null,[t,...e]),()=>Fu(...e),"number format",t=>t[ef](...e),uf,e=>Oi(e)||Ci(e))},$}function mf(e={}){const t=hf(function(e){const t=Oi(e.locale)?e.locale:au,n=Oi(e.fallbackLocale)||Ci(e.fallbackLocale)||Ii(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=Li(e.missing)?e.missing:void 0,o=!Ei(e.silentTranslationWarn)&&!mi(e.silentTranslationWarn)||!e.silentTranslationWarn,s=!Ei(e.silentFallbackWarn)&&!mi(e.silentFallbackWarn)||!e.silentFallbackWarn,l=!Ei(e.fallbackRoot)||e.fallbackRoot,a=!!e.formatFallbackMessages,i=Ii(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=Li(e.postTranslation)?e.postTranslation:void 0,f=!Oi(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,d=!Ei(e.sync)||e.sync;let h=e.messages;if(Ii(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce((e,n)=>{const r=e[n]||(e[n]={});return vi(r,t[n]),e},h||{})}const{__i18n:m,__root:g,__injectWithOption:v}=e,_=e.datetimeFormats,y=e.numberFormats;return{locale:t,fallbackLocale:n,messages:h,flatJson:e.flatJson,datetimeFormats:_,numberFormats:y,missing:r,missingWarn:o,fallbackWarn:s,fallbackRoot:l,fallbackFormat:a,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:f,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:d,__i18n:m,__root:g,__injectWithOption:v}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return Ei(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=Ei(e)?!e:e},get silentFallbackWarn(){return Ei(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=Ei(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return r.__extender=n,r}function gf(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[tf](t.pluralizationRules||e.pluralizationRules);const n=sf(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(t=>e.mergeLocaleMessage(t,n[t])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n])),t.numberFormats&&Object.keys(t.numberFormats).forEach(n=>e.mergeNumberFormat(n,t.numberFormats[n])),e}const vf={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function _f(){return so}const yf=Sn({name:"i18n-t",props:vi({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>hi(e)||!isNaN(e)}},vf),setup(e,t){const{slots:n,attrs:r}=t,o=e.i18n||Of({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(n).filter(e=>"_"!==e[0]),l=yi();e.locale&&(l.locale=e.locale),void 0!==e.plural&&(l.plural=Oi(e.plural)?+e.plural:e.plural);const a=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce((e,t)=>[...e,...t.type===so?t.children:[t]],[]);return t.reduce((t,n)=>{const r=e[n];return r&&(t[n]=r()),t},yi())}(t,s),i=o[Xu](e.keypath,a,l),c=vi(yi(),r);return qo(Oi(e.tag)||Fi(e.tag)?e.tag:_f(),c,i)}}});function bf(e,t,n,r){const{slots:o,attrs:s}=t;return()=>{const t={part:!0};let l=yi();e.locale&&(t.locale=e.locale),Oi(e.format)?t.key=e.format:Fi(e.format)&&(Oi(e.format.key)&&(t.key=e.format.key),l=Object.keys(e.format).reduce((t,r)=>n.includes(r)?vi(yi(),t,{[r]:e.format[r]}):t,yi()));const a=r(e.value,t,l);let i=[t.key];Ci(a)?i=a.map((e,t)=>{const n=o[e.type],r=n?n({[e.type]:e.value,index:t,parts:a}):[e.value];var s;return Ci(s=r)&&!Oi(s[0])&&(r[0].key=`${e.type}-${t}`),r}):Oi(a)&&(i=[a]);const c=vi(yi(),s);return qo(Oi(e.tag)||Fi(e.tag)?e.tag:_f(),c,i)}}const kf=Sn({name:"i18n-n",props:vi({value:{type:Number,required:!0},format:{type:[String,Object]}},vf),setup(e,t){const n=e.i18n||Of({useScope:e.scope,__useComponent:!0});return bf(e,t,Eu,(...e)=>n[ef](...e))}});function wf(e){if(Oi(e))return{path:e};if(Ii(e)){if(!("path"in e))throw Zu(Yu);return e}throw Zu(qu)}function xf(e){const{path:t,locale:n,args:r,choice:o,plural:s}=e,l={},a=r||{};return Oi(n)&&(l.locale=n),hi(o)&&(l.plural=o),hi(s)&&(l.plural=s),[t,a,l]}function Sf(e,t,...n){const r=Ii(n[0])?n[0]:{};(!Ei(r.globalInstall)||r.globalInstall)&&([yf.name,"I18nT"].forEach(t=>e.component(t,yf)),[kf.name,"I18nN"].forEach(t=>e.component(t,kf)),[Tf.name,"I18nD"].forEach(t=>e.component(t,Tf))),e.directive("t",function(e){const t=t=>{const{instance:n,value:r}=t;if(!n||!n.$)throw Zu(Ju);const o=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),s=wf(r);return[Reflect.apply(o.t,o,[...xf(s)]),o]};return{created:(n,r)=>{const[o,s]=t(r);fi&&e.global===s&&(n.__i18nWatcher=zr(s.locale,()=>{r.instance&&r.instance.$forceUpdate()})),n.__composer=s,n.textContent=o},unmounted:e=>{fi&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=wf(t);e.textContent=Reflect.apply(n.t,n,[...xf(r)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}(t))}const Cf=pi("global-vue-i18n");function Lf(e={}){const t=__VUE_I18N_LEGACY_API__&&Ei(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=!Ei(e.globalInjection)||e.globalInjection,r=new Map,[o,s]=function(e,t){const n=te(),r=__VUE_I18N_LEGACY_API__&&t?n.run(()=>mf(e)):n.run(()=>hf(e));if(null==r)throw Zu(Ju);return[n,r]}(e,t),l=pi("");const a={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(e,...r){if(e.__VUE_I18N_SYMBOL__=l,e.provide(e.__VUE_I18N_SYMBOL__,a),Ii(r[0])){const e=r[0];a.__composerExtend=e.__composerExtend,a.__vueI18nExtend=e.__vueI18nExtend}let o=null;!t&&n&&(o=function(e,t){const n=Object.create(null);Ef.forEach(e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw Zu(Ju);const o=kt(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,o)}),e.config.globalProperties.$i18n=n,Ff.forEach(n=>{const r=Object.getOwnPropertyDescriptor(t,n);if(!r||!r.value)throw Zu(Ju);Object.defineProperty(e.config.globalProperties,`$${n}`,r)});const r=()=>{delete e.config.globalProperties.$i18n,Ff.forEach(t=>{delete e.config.globalProperties[`$${t}`]})};return r}(e,a.global)),__VUE_I18N_FULL_INSTALL__&&Sf(e,a,...r),__VUE_I18N_LEGACY_API__&&t&&e.mixin(function(e,t,n){return{beforeCreate(){const r=Mo();if(!r)throw Zu(Ju);const o=this.$options;if(o.i18n){const r=o.i18n;if(o.__i18n&&(r.__i18n=o.__i18n),r.__root=t,this===this.$root)this.$i18n=gf(e,r);else{r.__injectWithOption=!0,r.__extender=n.__vueI18nExtend,this.$i18n=mf(r);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=gf(e,o);else{this.$i18n=mf({__i18n:o.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&af(t,o,o),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=Mo();if(!e)throw Zu(Ju);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),n.__deleteInstance(e),delete this.$i18n}}}(s,s.__composer,a));const i=e.unmount;e.unmount=()=>{o&&o(),a.dispose(),i()}},get global(){return s},dispose(){o.stop()},__instances:r,__getInstance:function(e){return r.get(e)||null},__setInstance:function(e,t){r.set(e,t)},__deleteInstance:function(e){r.delete(e)}};return a}function Of(e={}){const t=Mo();if(null==t)throw Zu(Gu);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Zu(zu);const n=function(e){const t=wr(e.isCE?Cf:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Zu(e.isCE?Ku:Ju);return t}(t),r=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),o=lf(t),s=function(e,t){return gi(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("global"===s)return af(r,e,o),r;if("parent"===s){let o=function(e,t,n=!1){let r=null;const o=t.root;let s=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=s;){const t=e;if("composition"===e.mode)r=t.__getInstance(s);else if(__VUE_I18N_LEGACY_API__){const e=t.__getInstance(s);null!=e&&(r=e.__composer,n&&r&&!r[nf]&&(r=null))}if(null!=r)break;if(o===s)break;s=s.parent}return r}(n,t,e.__useComponent);return null==o&&(o=r),o}const l=n;let a=l.__getInstance(t);if(null==a){const n=vi({},e);"__i18n"in o&&(n.__i18n=o.__i18n),r&&(n.__root=r),a=hf(n),l.__composerExtend&&(a[rf]=l.__composerExtend(a)),function(e,t,n){An(()=>{},t),Dn(()=>{const r=n;e.__deleteInstance(t);const o=r[rf];o&&(o(),delete r[rf])},t)}(l,t,a),l.__setInstance(t,a)}return a}const Ef=["locale","fallbackLocale","availableLocales"],Ff=["t","rt","d","n","tm","te"];const Tf=Sn({name:"i18n-d",props:vi({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},vf),setup(e,t){const n=e.i18n||Of({useScope:e.scope,__useComponent:!0});return bf(e,t,Su,(...e)=>n[Qu](...e))}});var Pf;if("boolean"!=typeof __VUE_I18N_FULL_INSTALL__&&(ki().__VUE_I18N_FULL_INSTALL__=!0),"boolean"!=typeof __VUE_I18N_LEGACY_API__&&(ki().__VUE_I18N_LEGACY_API__=!0),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(ki().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(ki().__INTLIFY_PROD_DEVTOOLS__=!1),cu=function(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&Oi(e)){!Ei(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||Mc)(e),r=Ac[n];if(r)return r;const{ast:o,detectError:s}=function(e,t={}){let n=!1;const r=t.onError||Qi;return t.onError=e=>{n=!0,r(e)},{...vc(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),l=Ic(o);return s?l:Ac[n]=l}{const t=e.cacheKey;if(t){const n=Ac[t];return n||(Ac[t]=Ic(e))}return Ic(e)}},uu=function(e,t){if(!Fi(e))return null;let n=su.get(t);if(n||(n=function(e){const t=[];let n,r,o,s,l,a,i,c=-1,u=0,f=0;const p=[];function d(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,o="\\"+t,p[0](),!0}for(p[0]=()=>{void 0===r?r=o:r+=o},p[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},p[2]=()=>{p[0](),f++},p[3]=()=>{if(f>0)f--,u=4,p[0]();else{if(f=0,void 0===r)return!1;if(r=ou(r),!1===r)return!1;p[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!d()){if(s=ru(n),i=tu[u],l=i[s]||i.l||8,8===l)return;if(u=l[0],void 0!==l[1]&&(a=p[l[1]],a&&(o=n,!1===a())))return;if(7===u)return t}}(t),n&&su.set(t,n)),!n)return null;const r=n.length;let o=e,s=0;for(;s<r;){const e=n[s];if(Tc.includes(e)&&_c(o))return null;const t=o[e];if(void 0===t)return null;if(Li(o))return null;o=t,s++}return o},fu=Zc,__INTLIFY_PROD_DEVTOOLS__){const e=ki();e.__INTLIFY__=!0,Pf=e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__,jc=Pf}export{zs as A,Ol as B,vo as C,ui as D,N as E,so as F,ct as G,Lo as H,nl as I,Kt as J,ii as K,ka as L,Wn as M,Xn as N,qn as O,Lf as P,sl as Q,pl as R,as as T,Dn as a,Sn as b,Yo as c,Ll as d,go as e,wo as f,xo as g,Co as h,sn as i,zn as j,Oo as k,Zn as l,fo as m,H as n,An as o,Lt as p,el as q,wt as r,ln as s,K as t,Of as u,Us as v,zr as w,Js as x,Hs as y,ci as z};

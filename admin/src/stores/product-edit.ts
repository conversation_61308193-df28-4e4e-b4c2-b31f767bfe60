import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import logger from '@/services/logger'
import { ApiService } from '@/services/api'
import { useProductStore } from '@/stores/products'

// Interface pour les réponses d'API
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any;
}

// Interface pour un produit en édition/création
interface ProductEditData {
  // Champs de base
  id?: number | string;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  image_url: string;
  color: string;
  group_id: number | null;
  
  // Tarification
  price: number;
  setup_fee: number;
  recurring: boolean;
  billing_cycle: string;
  price_monthly: number;
  price_quarterly: number;
  price_semiannually: number;
  price_annually: number;
  price_biennially: number;
  price_triennially: number;
  setup_fee_monthly: number;
  setup_fee_quarterly: number;
  setup_fee_semiannually: number;
  setup_fee_annually: number;
  setup_fee_biennially: number;
  setup_fee_triennially: number;

  // Configuration cycles multiples
  multiple_cycles_enabled: boolean;
  available_cycles: string;
  default_cycle: string;

  // Configuration
  product_type: string;
  status: string;
  welcome_email_id: number | null;
  hidden: boolean;
  featured: boolean;
  features: any[];
  options: any;
  
  // Stock et provisioning
  stock_control: boolean;
  stock_quantity: number;
  auto_provision: boolean;
  provisioning_type: string;
  server_type: string;
  server_id: string;
  package_name: string;
}

export const useProductEditStore = defineStore('productEdit', () => {
  // État
  const product = ref<ProductEditData | null>(null)
  const mode = ref<'create' | 'edit'>('create')
  const loading = ref<boolean>(false)
  const saving = ref<boolean>(false)
  const errors = ref<Record<string, string>>({})
  const originalProduct = ref<ProductEditData | null>(null) // Pour détecter les changements

  // Getters
  const isCreateMode = computed(() => mode.value === 'create')
  const isEditMode = computed(() => mode.value === 'edit')
  const hasChanges = computed(() => {
    if (!originalProduct.value || !product.value) return false
    return JSON.stringify(originalProduct.value) !== JSON.stringify(product.value)
  })
  const isValid = computed(() => {
    return Object.keys(errors.value).length === 0 && 
           product.value?.name && 
           product.value?.name.trim().length > 0
  })

  // Fonction pour créer un produit vide avec valeurs par défaut
  const createEmptyProduct = (): ProductEditData => ({
    name: '',
    slug: '',
    description: '',
    short_description: '',
    image_url: '',
    color: '#0066ff',
    group_id: null,
    price: 0,
    setup_fee: 0,
    recurring: true,
    billing_cycle: 'monthly',
    price_monthly: 0,
    price_quarterly: 0,
    price_semiannually: 0,
    price_annually: 0,
    price_biennially: 0,
    price_triennially: 0,
    setup_fee_monthly: 0,
    setup_fee_quarterly: 0,
    setup_fee_semiannually: 0,
    setup_fee_annually: 0,
    setup_fee_biennially: 0,
    setup_fee_triennially: 0,

    // Configuration cycles multiples
    multiple_cycles_enabled: false,
    available_cycles: 'monthly',
    default_cycle: 'monthly',

    product_type: 'shared_hosting',
    status: 'active',
    welcome_email_id: null,
    hidden: false,
    featured: false,
    features: [],
    options: {},
    stock_control: false,
    stock_quantity: 0,
    auto_provision: false,
    provisioning_type: 'manual',
    server_type: '',
    server_id: '',
    package_name: ''
  })

  // Actions
  
  /**
   * Initialise le store pour la création d'un nouveau produit
   */
  const initCreate = () => {
    logger.info('Initialisation du mode création de produit')
    mode.value = 'create'
    product.value = createEmptyProduct()
    originalProduct.value = null
    errors.value = {}
    loading.value = false
    saving.value = false
  }

  /**
   * Initialise le store pour l'édition d'un produit existant
   */
  const initEdit = async (productId: number | string) => {
    try {
      loading.value = true
      errors.value = {}
      mode.value = 'edit'
      
      logger.info('Initialisation du mode édition de produit', { productId })
      
      // Utiliser le store products pour récupérer le produit (avec cache)
      const productStore = useProductStore()
      const fetchedProduct = await productStore.fetchProduct(productId)
      
      if (!fetchedProduct) {
        throw new Error('Produit non trouvé')
      }
      
      // Mapper les données du produit vers notre interface avec conversion des types
      const mappedProduct = {
        ...createEmptyProduct(),
        ...fetchedProduct
      }

      // Conversion des champs boolean (de string vers boolean) si ils existent
      if ('featured' in fetchedProduct) {
        mappedProduct.featured = Boolean(Number((fetchedProduct as any).featured))
      }
      if ('hidden' in fetchedProduct) {
        mappedProduct.hidden = Boolean(Number((fetchedProduct as any).hidden))
      }
      if ('recurring' in fetchedProduct) {
        mappedProduct.recurring = Boolean(Number(fetchedProduct.recurring))
      }
      if ('multiple_cycles_enabled' in fetchedProduct) {
        mappedProduct.multiple_cycles_enabled = Boolean(Number((fetchedProduct as any).multiple_cycles_enabled))
      }

      product.value = mappedProduct
      
      // Sauvegarder l'état original pour détecter les changements
      originalProduct.value = JSON.parse(JSON.stringify(product.value))
      
      logger.info('Produit chargé pour édition', { 
        productId, 
        productName: product.value.name 
      })
      
    } catch (error) {
      logger.error('Erreur lors du chargement du produit pour édition', { 
        productId, 
        error: error instanceof Error ? error.message : error 
      })
      errors.value.general = 'Impossible de charger le produit'
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * Met à jour un champ du produit
   */
  const updateField = (field: keyof ProductEditData, value: any) => {
    if (!product.value) return

    logger.debug('Mise à jour du champ produit', { field, value })

    // Log spécial pour les checkboxes
    if (field === 'featured' || field === 'hidden') {
      logger.info(`Checkbox ${field} mise à jour`, {
        field,
        oldValue: product.value[field],
        newValue: value,
        type: typeof value
      })
    }

    // Mise à jour du champ
    ;(product.value as any)[field] = value
    
    // Génération automatique du slug si on modifie le nom
    if (field === 'name' && typeof value === 'string') {
      product.value.slug = value
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
    }
    
    // Effacer l'erreur du champ si elle existe
    if (errors.value[field]) {
      delete errors.value[field]
    }
  }

  /**
   * Valide les données du produit
   */
  const validateProduct = (): boolean => {
    errors.value = {}
    
    if (!product.value) {
      errors.value.general = 'Aucune donnée de produit'
      return false
    }
    
    // Validation du nom
    if (!product.value.name || product.value.name.trim().length === 0) {
      errors.value.name = 'Le nom du produit est requis'
    }
    
    // Validation du prix
    if (product.value.price < 0) {
      errors.value.price = 'Le prix ne peut pas être négatif'
    }
    
    // Validation du groupe
    if (!product.value.group_id) {
      errors.value.group_id = 'Veuillez sélectionner une catégorie'
    }
    
    return Object.keys(errors.value).length === 0
  }

  /**
   * Sauvegarde le produit (création ou mise à jour)
   */
  const saveProduct = async (): Promise<ApiResponse<any>> => {
    try {
      saving.value = true
      
      // Validation
      if (!validateProduct()) {
        logger.warn('Validation échouée lors de la sauvegarde', { errors: errors.value })
        return {
          success: false,
          message: 'Veuillez corriger les erreurs avant de sauvegarder'
        }
      }
      
      logger.info('Début de la sauvegarde du produit', {
        mode: mode.value,
        productName: product.value?.name
      })

      // Conversion des booleans en entiers pour la base de données
      const productData = {
        ...product.value,
        recurring: product.value?.recurring ? 1 : 0,
        multiple_cycles_enabled: product.value?.multiple_cycles_enabled ? 1 : 0,
        hidden: product.value?.hidden ? 1 : 0,
        featured: product.value?.featured ? 1 : 0,
        stock_control: product.value?.stock_control ? 1 : 0,
        auto_provision: product.value?.auto_provision ? 1 : 0
      }

      logger.info('Données converties pour sauvegarde', {
        originalBooleans: {
          recurring: product.value?.recurring,
          multiple_cycles_enabled: product.value?.multiple_cycles_enabled
        },
        convertedIntegers: {
          recurring: productData.recurring,
          multiple_cycles_enabled: productData.multiple_cycles_enabled
        }
      })

      const productStore = useProductStore()
      let result: ApiResponse<any>

      if (isCreateMode.value) {
        // Création
        result = await productStore.createProduct(productData)
      } else {
        // Mise à jour
        if (!productData?.id) {
          throw new Error('ID du produit manquant pour la mise à jour')
        }
        result = await productStore.updateProduct(productData.id, productData)
      }
      
      if (result.success) {
        logger.info('Produit sauvegardé avec succès', { 
          mode: mode.value,
          productId: result.data?.id 
        })
        
        // Mettre à jour l'état original après sauvegarde réussie
        if (product.value) {
          originalProduct.value = JSON.parse(JSON.stringify(product.value))
        }
      }
      
      return result
      
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde du produit', { 
        error: error instanceof Error ? error.message : error 
      })
      return {
        success: false,
        message: 'Erreur lors de la sauvegarde du produit'
      }
    } finally {
      saving.value = false
    }
  }

  /**
   * Réinitialise le store
   */
  const reset = () => {
    product.value = null
    originalProduct.value = null
    mode.value = 'create'
    loading.value = false
    saving.value = false
    errors.value = {}
    logger.info('Store product-edit réinitialisé')
  }

  return {
    // État
    product,
    mode,
    loading,
    saving,
    errors,
    
    // Getters
    isCreateMode,
    isEditMode,
    hasChanges,
    isValid,
    
    // Actions
    initCreate,
    initEdit,
    updateField,
    validateProduct,
    saveProduct,
    reset
  }
})

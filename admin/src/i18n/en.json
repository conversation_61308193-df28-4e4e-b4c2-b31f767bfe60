{"common": {"select": "Select", "search": "Search", "view_all": "View all", "client": "Client", "email": "Email", "status": "Status", "date": "Date", "back": "Back", "success": "Success", "error": "Error", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "create": "Create", "update": "Update", "confirm": "Confirm", "yes": "Yes", "no": "No", "name": "Name", "description": "Description", "price": "Price", "actions": "Actions", "retry": "Retry", "configure": "Configure", "errorLoading": "Error loading", "close": "Close", "noData": "No data available", "active": "Active", "inactive": "Inactive", "online": "Online", "offline": "Offline", "minutes": "minutes", "minute": "minute", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "confirmDelete": "Confirm deletion", "activate": "Activate", "deactivate": "Deactivate", "never": "Never", "saving": "Saving...", "error_occurred": "An error occurred", "language": {"select": "Choose language", "fr": "Français", "en": "English"}, "simple": "Simple", "advanced": "Advanced"}, "notFound": {"title": "404 - Page Not Found", "description": "The page you are looking for does not exist or has been moved.", "whatHappened": "What happened?", "urlNotFound": "The URL you entered was not found on this server.", "suggestions": "Suggestions:", "checkUrl": "Check that the URL is correctly entered", "goBack": "Go back to the previous page", "goToDashboard": "Go to the dashboard", "backButton": "Back", "dashboardButton": "Dashboard"}, "dashboard": {"title": "Dashboard", "subtitle": "Overview of your activity", "refresh": "Refresh", "viewMore": "View more", "stats": {"active_clients": "Active clients", "active_services": "Active services", "active_tickets": "Active tickets", "monthly_revenue": "Monthly revenue"}, "recent_clients": {"title": "Recent clients"}, "recent_tickets": {"title": "Recent tickets"}, "chart": {"today": "Today", "week": "7 Days", "month": "30 Days", "year": "This year", "revenue": "Revenue", "evolution": "Revenue evolution"}}, "login": {"title": "<PERSON><PERSON>", "email": "Email address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signIn": "Sign in", "errorFields": "Please fill in all fields", "resetTitle": "Reset password", "resetDescription": "Enter your email address to receive a reset link", "sendReset": "Send reset link", "copyright": "© {year} TechCMS. All rights reserved."}, "clients": {"title": "Clients", "description": "Manage clients and their information", "add_new": "New client", "confirm_delete": "Are you sure you want to delete this client?", "filters": {"search": "Search", "search_placeholder": "Search by name, email, company...", "status": "Status", "service_type": "Service type"}, "table": {"client": "Client", "company": "Company", "name": "Name", "email": "Email", "phone": "Phone", "services": "Services", "status": "Status", "created": "Created", "actions": "Actions"}, "status": {"all": "All statuses", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "pending": "Pending"}, "services": {"all": "All types", "hosting": "Hosting", "domain": "Domain", "ssl": "SSL"}, "no_services": "No services", "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "add": {"title": "New client", "description": "Create a new client account"}, "edit": {"title": "Edit client", "description": "Edit client information"}, "details": {"title": "Client details", "description": "Detailed client information", "error_loading": "Error loading client details", "personal_info": "Personal information", "contact_info": "Contact information", "billing_info": "Billing information", "services_info": "Services", "notes_info": "Notes", "activity_info": "Recent activity", "no_services": "No active services", "no_notes": "No notes", "no_activity": "No recent activity", "delete_confirm": "Are you sure you want to delete this client?", "delete_warning": "This action is irreversible and will delete all associated data."}, "form": {"personal_info": "Personal information", "contact_info": "Contact information", "billing_info": "Billing information", "account_info": "Account information", "address_title": "Address", "firstname": "First name", "firstname_placeholder": "Enter first name", "lastname": "Last name", "lastname_placeholder": "Enter last name", "email": "Email", "email_placeholder": "Enter email address", "phone": "Phone", "phone_placeholder": "****** 567 8900", "company": "Company", "company_placeholder": "Company name (optional)", "address": "Address", "address_placeholder": "123 Main Street", "city": "City", "city_placeholder": "New York", "state": "State/Province", "postal_code": "Postal code", "postal_code_placeholder": "10001", "postcode": "Postal code", "country": "Country", "country_placeholder": "United States", "password": "Password", "password_placeholder": "Secure password", "password_optional": "(optional)", "password_leave_empty": "Leave empty to keep unchanged", "confirm_password": "Confirm password", "status": "Status", "notes": "Notes", "firstname_required": "First name is required", "lastname_required": "Last name is required", "email_required": "Email is required", "email_invalid": "Invalid email format", "phone_required": "Phone is required", "password_required": "Password is required", "password_min": "Password must be at least 8 characters", "password_mismatch": "Passwords do not match", "country_required": "Country is required", "save": "Save", "saving": "Saving...", "saved_successfully": "Client saved successfully", "save_error": "Error saving client"}, "empty": {"title": "No clients found", "description": "No clients match the search criteria"}}, "products_services": {"title": "Products & Services", "description": "Manage available products and services", "add_new": "New product", "create_title": "Create new product", "edit_title": "Edit product", "type_selection_help": "Select the type of product you want to create. Each type has its own configuration options.", "confirm_delete": "Are you sure you want to delete this product?", "filters": {"search": "Search", "type": "Type", "status": "Status", "group": "Group", "price_range": "Price range", "all_types": "All types", "all_prices": "All prices", "price_low": "€0 - €50", "price_medium": "€50 - €200", "price_high": "€200 - €1000"}, "types": {"reseller": "Reseller hosting", "dedicated": "Dedicated server", "vps": "VPS", "shared": "Shared hosting"}, "types_descriptions": {"shared_hosting": "Affordable and easy-to-use hosting for small websites.", "reseller_hosting": "Host your own clients with our reseller hosting plans.", "server_vps": "A virtual private server for more power and flexibility.", "domain": "Register or transfer your domain name.", "other": "Other types of products and services."}, "table": {"name": "Name", "type": "Type", "price": "Price", "status": "Status", "clients": "Clients", "actions": "Actions"}, "status": {"all": "All", "active": "Active", "inactive": "Inactive", "maintenance": "Maintenance"}, "errors": {"fetch": "Error fetching products", "delete": "Error deleting product", "load_failed": "Failed to load products", "modules_load": "Error loading server modules", "type_required": "Please select a product type", "save_failed": "Error saving data", "name_required": "Product name is required"}, "error_loading_data": "Error loading data", "error_saving_data": "Error saving data", "no_products_found": "No products found", "try_different_filter": "Try a different filter", "module": {"configuration": "Module configuration", "loading_config": "Loading configuration...", "select_plan": "Plan/Template", "select_plan_option": "Select a plan", "operating_system": "Operating system", "select_os": "Select an OS", "no_config_available": "No specific configuration available for this module", "disk_space": "Disk space", "disk_space_hint": "In MB (0 for unlimited)", "bandwidth": "Bandwidth", "bandwidth_hint": "In MB (0 for unlimited)", "allow_subdomains": "Allow subdomains", "allow_php": "Allow PHP", "max_email_accounts": "Maximum email accounts", "max_databases": "Maximum databases", "module_required": "A module must be selected", "provision_option_required": "A provisioning option must be selected", "server_group_required": "A server group must be selected", "saved_successfully": "Module configuration saved successfully", "cpanel_description": "Provisioning module for cPanel/WHM", "plesk_description": "Provisioning module for Plesk", "directadmin_description": "Provisioning module for DirectAdmin", "proxmox_description": "Provisioning module for Proxmox VE", "solusvm_description": "Provisioning module for SolusVM", "tab_general": "General", "tab_features": "Features", "tab_limits": "Limits", "description": "Configure the provisioning module for this product", "select_module": "Select a module", "select_module_placeholder": "Choose a provisioning module", "provision_options": "Provisioning options", "server_group": "Server group", "select_server_group": "Select a server group", "select_server": "Select a server", "select_server_is_required": "Selecting a server is required.", "automation": "Automation", "auto_setup_on_order": "Auto setup on order", "auto_setup_on_payment": "Auto setup on payment", "auto_setup_on_pending": "Auto setup on pending", "no_auto_setup": "No auto setup", "welcome_email": "Welcome email", "select_welcome_email": "Select an email template", "custom_module": "Custom module", "custom_description": "Custom module configuration", "provision_settings": "Provisioning settings", "auto_provision": "Auto provision", "auto_provision_description": "Automatically provision the service on order", "manual_provision": "Manual provision", "manual_provision_description": "Manually provision the service after order"}, "modules": {"provisioning_options": "Provisioning options"}, "tabs": {"type": "Type", "details": "Details", "pricing": "Pricing", "module": "<PERSON><PERSON><PERSON>", "custom_fields": "Custom fields", "configurable_options": "Configurable options", "upgrades": "Upgrades", "freedomain": "Free domain", "cross_sells": "Cross sells", "other": "Other options", "links": "Useful links"}, "all_categories": "All categories", "upgrades": {"description": "Configure available upgrades for this product", "select_products": "Select products", "no_products_selected": "No products selected", "select_products_description": "Select products that clients can upgrade to"}, "details": {"description": "Description", "basic_info": "Basic information", "name": "Product name", "name_placeholder": "Enter product name", "name_required": "Product name is required", "slug": "Slug (URL)", "slug_placeholder": "product-slug", "slug_hint": "Used in the product URL. Leave empty to auto-generate.", "category": "Category", "select_category": "Select a category", "category_required": "Category is required", "description_placeholder": "Detailed product description", "short_description": "Short description", "short_description_placeholder": "Product summary", "display_options": "Display options", "welcome_email": "Welcome email", "select_welcome_email": "Select an email", "color": "Color", "image": "Product image", "remove_image": "Remove image", "hidden": "Hidden product", "hidden_hint": "Product will not be visible in the store", "featured": "Featured product", "featured_hint": "Product will be displayed with priority", "additional_info": "Additional information", "tag_line": "Tag line", "tag_line_placeholder": "Catchy slogan", "order_link": "Custom order link", "order_link_placeholder": "https://example.com/order", "order_link_hint": "External link to order this product", "saved_successfully": "Product details saved successfully"}, "columns": {"name": "Name", "description": "Description", "type": "Type", "price": "Price", "status": "Status", "group": "Group", "clients": "Clients"}, "groups": {"all": "All groups", "none": "No group"}, "actions": {"edit": "Edit", "delete": "Delete"}, "empty": {"title": "No products found", "description": "No products match the search criteria"}, "product_created_successfully": "Product created successfully", "product_updated_successfully": "Product updated successfully", "search": "Search for a product...", "add": "New product", "pricing": {"description": "Configure pricing and billing options for your product", "setup_fee": "Setup fee", "setup_fee_hint": "One-time fee charged on first order", "base_price": "Base price", "base_price_hint": "Main product price", "recurring_options": "Recurring billing options", "recurring_pricing": "Recurring billing", "monthly": "Monthly", "quarterly": "Quarterly", "semiannually": "Semi-annually", "annually": "Annually", "biennially": "Biennially", "triennially": "Triennially", "default_cycle": "Default billing cycle", "allow_cycle_change": "Allow cycle change", "allow_cycle_change_hint": "Allow customers to change their billing cycle", "tax_options": "Tax options", "tax_included": "Tax included", "tax_included_hint": "Displayed prices include tax", "tax_exempt": "Tax exempt", "tax_exempt_hint": "This product is exempt from tax", "price_not_negative": "Prices cannot be negative", "saved_successfully": "Pricing saved successfully"}, "custom_fields": {"description": "Configure custom fields for this product", "add_field": "Add field", "field_name": "Field name", "field_name_placeholder": "Custom field name", "field_type": "Field type", "field_description": "Description", "field_description_placeholder": "Field description", "required": "Required", "admin_only": "Admin only", "show_on_order": "Show on order", "show_on_invoice": "Show on invoice", "field_types": {"text": "Text", "password": "Password", "dropdown": "Dropdown", "textarea": "Textarea", "checkbox": "Checkbox", "radio": "Radio button"}, "options": "Options", "options_placeholder": "Option 1|Option 2|Option 3", "options_hint": "Separate options with |", "validation": "Validation", "regex_pattern": "Regex pattern", "error_message": "Error message", "saved_successfully": "Custom fields saved successfully", "no_fields": "No custom fields", "no_fields_description": "No custom fields have been configured for this product"}, "configurable_options": {"description": "Configure configurable options for this product", "add_option": "Add option", "option_name": "Option name", "option_name_placeholder": "Configurable option name", "option_type": "Option type", "option_description": "Description", "option_description_placeholder": "Option description", "option_types": {"dropdown": "Dropdown", "radio": "Radio button", "checkbox": "Checkboxes", "quantity": "Quantity", "slider": "Slide<PERSON>"}, "sub_options": "Sub-options", "add_sub_option": "Add sub-option", "sub_option_name": "Name", "sub_option_price": "Price", "sub_option_setup_fee": "Setup fee", "monthly_price": "Monthly price", "quarterly_price": "Quarterly price", "semiannual_price": "Semi-annual price", "annual_price": "Annual price", "min_quantity": "Minimum quantity", "max_quantity": "Maximum quantity", "saved_successfully": "Configurable options saved successfully", "no_options": "No configurable options", "no_options_description": "No configurable options have been configured for this product"}, "freedomain": {"description": "Configure free domain options for this product", "enable_freedomain": "Enable free domain", "enable_freedomain_hint": "Offer a free domain with this product", "domain_types": "Domain types", "register_domain": "Register new domain", "transfer_domain": "Transfer existing domain", "subdomain": "Subdomain", "allowed_tlds": "Allowed extensions", "allowed_tlds_placeholder": ".com,.net,.org", "allowed_tlds_hint": "Allowed domain extensions, separated by commas", "subdomain_options": "Subdomain options", "subdomain_prefix": "Subdomain prefix", "subdomain_prefix_placeholder": "client", "subdomain_domain": "Main domain", "subdomain_domain_placeholder": "mydomain.com", "saved_successfully": "Free domain options saved successfully"}, "cross_sells": {"description": "Configure cross-sell products for this product", "add_product": "Add product", "search_products": "Search products", "selected_products": "Selected products", "no_products_selected": "No products selected", "product_name": "Product name", "product_price": "Price", "remove_product": "Remove product", "saved_successfully": "Cross sells saved successfully", "tab_cross_sells": "Cross sells", "tab_related": "Related products", "no_related": "No related products", "no_related_description": "No related products have been configured for this product", "no_cross_sells": "No cross sells", "no_cross_sells_description": "No cross sells have been configured for this product", "available_products": "Available products"}, "other": {"description": "Configure other options for this product", "configuration_title": "Configuration", "require_domain": "Require domain", "require_domain_help": "A domain is required to order this product", "auto_setup": "Auto setup", "auto_setup_help": "Automatically setup the service after order", "stock_control": "Stock control", "enable_stock_control": "Enable stock control", "stock_quantity": "Stock quantity", "stock_quantity_hint": "Number of units available", "low_stock_threshold": "Low stock threshold", "low_stock_threshold_hint": "Alert when stock reaches this level", "stock_control_help": "Manage available stock for this product", "auto_provisioning": "Auto provisioning", "enable_auto_provisioning": "Enable auto provisioning", "provisioning_delay": "Provisioning delay", "provisioning_delay_hint": "Delay in minutes before provisioning", "retirement_options": "Retirement options", "retirement_date": "Retirement date", "retirement_date_hint": "Date from which the product will no longer be available", "replacement_product": "Replacement product", "select_replacement": "Select a replacement product", "hidden": "Hidden product", "hidden_help": "Hide this product from the store", "welcome_email": "Welcome email", "welcome_email_none": "No email", "welcome_email_help": "Email sent after service activation", "notes": "Notes", "notes_placeholder": "Product notes...", "notes_help": "Internal notes about this product", "saved_successfully": "Other options saved successfully"}, "links": {"description": "Configure useful links for this product", "add_link": "Add link", "link_title": "Link title", "link_title_placeholder": "Documentation", "link_url": "Link URL", "link_url_placeholder": "https://example.com/docs", "link_description": "Description", "link_description_placeholder": "Link description", "link_target": "Link target", "target_self": "Same window", "target_blank": "New window", "link_icon": "Icon", "link_icon_placeholder": "fas fa-book", "remove_link": "Remove link", "saved_successfully": "Useful links saved successfully", "direct_links_title": "Direct links", "direct_cart_link": "Direct cart link", "direct_cart_link_placeholder": "e.g., https://yourstore.com/cart.php?a=add&pid=1", "direct_cart_link_template": "Cart link template", "direct_cart_link_template_placeholder": "e.g., cart.php?a=add&pid={PRODUCT_ID}", "direct_cart_link_domain": "Cart link domain", "direct_cart_link_domain_placeholder": "e.g., yourstore.com", "group_cart_link": "Group cart link", "group_cart_link_placeholder": "e.g., https://yourstore.com/cart.php?gid=1", "product_links_title": "Product links", "url": "URL", "url_placeholder": "e.g., https://example.com/product/docs", "visits": "Visits", "actions": "Actions"}}, "product_groups": {"add": "New group", "success": {"create": "Group created successfully", "update": "Group updated successfully", "delete": "Group deleted successfully"}, "errors": {"create": "Error creating group", "update": "Error updating group", "delete": "Error deleting group"}, "confirm_delete": "Are you sure you want to delete this group?"}, "services": {"title": "Services", "description": "Manage client services", "add_new": "New service", "table": {"client": "Client", "product": "Product", "domain": "Domain", "status": "Status", "next_due_date": "Next due date", "recurring_amount": "Recurring amount", "actions": "Actions"}, "status": {"all": "All statuses", "pending": "Pending", "active": "Active", "suspended": "Suspended", "cancelled": "Cancelled", "terminated": "Terminated", "fraud": "<PERSON><PERSON>"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "activate": "Activate", "suspend": "Suspend", "cancel": "Cancel", "terminate": "Terminate"}, "create": {"title": "New service", "description": "Create a new client service"}, "edit": {"title": "Edit service", "description": "Edit service information"}, "details": {"title": "Service details", "description": "Detailed service information", "unknown_product": "Unknown product", "client": "Client", "client_name": "Client name", "client_email": "Client email", "unknown": "Unknown", "view_client": "View client", "product": "Product", "product_name": "Product name", "view_product": "View product", "server": "Server", "server_name": "Server name", "view_server": "View server", "no_server": "No server assigned", "billing": "Billing", "billing_cycle": "Billing cycle", "next_due_date": "Next due date", "not_set": "Not set", "recurring_amount": "Recurring amount", "setup_fee": "Setup fee", "important_dates": "Important dates", "created_at": "Created at", "suspension_date": "Suspension date", "cancellation_date": "Cancellation date", "termination_date": "Termination date", "login_details": "Login details", "username": "Username", "password": "Password", "no_login_details": "No login details", "configuration": "Configuration", "notes": "Notes", "error_loading": "Error loading service"}, "filters": {"search": "Search", "search_placeholder": "Search by client, product, domain...", "status": "Status", "client": "Client", "product": "Product", "all_clients": "All clients", "all_products": "All products"}, "empty": {"title": "No services found", "message": "No services match the search criteria"}, "delete_modal": {"title": "Delete service", "confirmation": "Are you sure you want to delete this service?", "warning": "This action is irreversible and will delete all associated data."}, "status_change": {"activate_title": "Activate service", "suspend_title": "Suspend service", "cancel_title": "Cancel service", "terminate_title": "Terminate service", "activate_confirmation": "Are you sure you want to activate this service?", "suspend_confirmation": "Are you sure you want to suspend this service?", "cancel_confirmation": "Are you sure you want to cancel this service?", "terminate_confirmation": "Are you sure you want to terminate this service? This action is irreversible.", "notes": "Notes (optional)", "notes_placeholder": "Reason for status change...", "success_title": "Status changed", "activate_success": "Service has been activated successfully", "suspend_success": "Service has been suspended successfully", "cancel_success": "Service has been cancelled successfully", "terminate_success": "Service has been terminated successfully", "error": "Error changing status", "invalid_action": "Invalid action"}, "billing_cycles": {"monthly": "Monthly", "quarterly": "Quarterly", "semi_annually": "Semi-annually", "annually": "Annually", "biennially": "Biennially", "triennially": "Triennially"}, "form": {"section": {"client_product": "Client and product", "service_details": "Service details", "details": "Service details", "billing": "Billing", "configuration": "Configuration", "configurations": "Configurations", "notes": "Notes"}, "client": "Client", "select_client": "Select a client", "product": "Product", "select_product": "Select a product", "domain": "Domain", "domain_placeholder": "example.com", "username": "Username", "username_placeholder": "Service username", "password": "Password", "password_placeholder": "Service password", "server": "Server", "select_server": "Select a server", "status": "Status", "billing_cycle": "Billing cycle", "next_due_date": "Next due date", "recurring_amount": "Recurring amount", "amount_placeholder": "0.00", "setup_fee": "Setup fee", "fee_placeholder": "0.00", "notes": "Notes", "notes_placeholder": "Service notes...", "config_name": "Configuration name", "config_value": "Value", "action": "Action", "add_config": "Add configuration", "calculate_due_date": "Recalculate due date", "error_loading": "Error loading data", "error_submit": "Error submitting form", "success_create_title": "Service created", "success_create_message": "Service has been created successfully", "success_update_title": "Service updated", "success_update_message": "Service has been updated successfully", "errors": {"client_required": "Client is required", "product_required": "Product is required", "status_required": "Status is required", "invalid_date": "Invalid date format", "negative_amount": "Amount cannot be negative", "negative_fee": "Fee cannot be negative"}}}, "invoices": {"title": "Invoices", "description": "Manage invoices and billing", "add_new": "New invoice", "create_title": "New invoice", "create_description": "Create a new invoice", "edit_title": "Edit invoice", "edit_description": "Edit invoice information", "details_title": "Invoice #{id}", "details_description": "Invoice details", "actions": {"add": "New invoice", "edit": "Edit", "mark_paid": "Mark as paid", "download_pdf": "Download PDF"}, "search": {"placeholder": "Search by number, client..."}, "filters": {"search": "Search", "status": "Status", "date_range": "Date range", "all_time": "All time", "this_month": "This month", "last_month": "Last month", "this_year": "This year", "last_year": "Last year"}, "table": {"number": "Number", "client": "Client", "amount": "Amount", "status": "Status", "date": "Date", "due_date": "Due date", "actions": "Actions"}, "columns": {"service": "Service", "amount": "Amount", "due_date": "Due date", "status": "Status", "paid_date": "Paid date", "created_at": "Created at"}, "status": {"all": "All statuses", "draft": "Draft", "unpaid": "Unpaid", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled"}, "empty": {"title": "No invoices found", "description": "No invoices match the search criteria"}, "not_found": {"title": "Invoice not found", "description": "The requested invoice does not exist or has been deleted."}, "details": {"main_info": "Main information", "id": "Number", "status": "Status", "date": "Date", "due_date": "Due date", "amount": "Amount", "client_info": "Client information", "client": "Client", "email": "Email", "service": "Service", "items": "Invoice items", "description": "Description", "quantity": "Quantity", "unit_price": "Unit price", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "payments": "Associated payments", "notes": "Notes", "history": "History"}, "history": {"created": "Invoice created", "updated": "Invoice updated", "paid": "Invoice paid"}, "form": {"client_info": "Client information", "client": "Client", "select_client": "Select a client", "service": "Service", "select_service": "Select a service", "invoice_details": "Invoice details", "date": "Date", "due_date": "Due date", "status": "Status", "amount": "Amount", "items": "Invoice items", "item_description": "Description", "item_description_placeholder": "Item description...", "quantity": "Quantity", "unit_price": "Unit price", "total": "Total", "add_item": "Add item", "notes": "Notes", "notes_placeholder": "Invoice notes...", "subtotal": "Subtotal", "tax": "Tax"}, "modal": {"add_title": "New invoice", "edit_title": "Edit invoice", "view_title": "Invoice details"}, "confirm_mark_paid": "Are you sure you want to mark this invoice as paid?", "success": {"created": "Invoice created successfully", "updated": "Invoice updated successfully", "marked_paid": "Invoice marked as paid"}, "errors": {"load_failed": "Error loading invoice", "create_failed": "Error creating invoice", "update_failed": "Error updating invoice", "mark_paid_failed": "Error marking as paid", "download_failed": "Error downloading"}}, "payments": {"title": "Payments", "description": "Manage payments and transactions", "add_new": "New payment", "create_title": "New payment", "create_description": "Create a new payment", "edit_title": "Edit payment", "edit_description": "Edit payment information", "details_title": "Payment #{id}", "details_description": "Payment details", "actions": {"add": "New payment", "edit": "Edit", "refund": "Refund"}, "search": {"placeholder": "Search by transaction, client..."}, "filters": {"search": "Search", "status": "Status", "method": "Method", "gateway": "Gateway", "date_range": "Date range", "all_time": "All time", "this_month": "This month", "last_month": "Last month", "this_year": "This year", "last_year": "Last year"}, "table": {"transaction_id": "Transaction ID", "client": "Client", "invoice": "Invoice", "amount": "Amount", "method": "Method", "gateway": "Gateway", "status": "Status", "date": "Date", "actions": "Actions"}, "status": {"all": "All statuses", "pending": "Pending", "completed": "Completed", "failed": "Failed", "refunded": "Refunded"}, "methods": {"all": "All methods", "bank_transfer": "Bank transfer", "cash": "Cash"}, "empty": {"title": "No payments found", "description": "No payments match the search criteria"}, "not_found": {"title": "Payment not found", "description": "The requested payment does not exist or has been deleted."}, "details": {"main_info": "Main information", "id": "Number", "status": "Status", "amount": "Amount", "method": "Method", "date": "Date", "transaction_id": "Transaction ID", "client_invoice": "Client and invoice", "client": "Client", "invoice": "Invoice", "invoice_total": "Invoice total", "notes": "Notes", "history": "History"}, "history": {"created": "Payment created", "updated": "Payment updated", "refunded": "Payment refunded"}, "form": {"basic_info": "Basic information", "invoice": "Invoice", "select_invoice": "Select an invoice", "amount": "Amount", "method": "Payment method", "select_method": "Select a method", "status": "Status", "additional_info": "Additional information", "transaction_id": "Transaction ID", "transaction_id_placeholder": "Transaction identifier...", "notes": "Notes", "notes_placeholder": "Payment notes..."}, "modal": {"add_title": "New payment", "edit_title": "Edit payment", "view_title": "Payment details"}, "confirm_refund": "Are you sure you want to refund this payment?", "success": {"created": "Payment created successfully", "updated": "Payment updated successfully", "refunded": "Payment refunded successfully"}, "errors": {"load_failed": "Error loading payment", "create_failed": "Error creating payment", "update_failed": "Error updating payment", "refund_failed": "Error processing refund"}}, "tickets": {"title": "Support tickets", "description": "Manage client support requests", "add_new": "New ticket", "create_title": "New ticket", "create_description": "Create a new support ticket", "edit_title": "Edit ticket", "edit_description": "Edit ticket information", "details_title": "Ticket #{id}", "details_description": "Support ticket details", "actions": {"add": "New ticket", "create": "Create ticket", "update": "Update", "reply": "Reply", "edit": "Edit", "close": "Close", "reopen": "Reopen", "assign": "Assign"}, "filters": {"search": "Search", "status": "Status", "priority": "Priority", "department": "Department", "assigned_to": "Assigned to", "all_time": "All time", "this_month": "This month", "last_month": "Last month", "this_year": "This year", "last_year": "Last year"}, "search": {"placeholder": "Search by subject, client..."}, "table": {"subject": "Subject", "client": "Client", "status": "Status", "priority": "Priority", "department": "Department", "assigned_to": "Assigned to", "last_reply": "Last reply", "created_at": "Created at", "created": "Created", "updated": "Updated", "actions": "Actions"}, "status": {"all": "All statuses", "open": "Open", "pending": "Pending", "answered": "Answered", "customer-reply": "Customer reply", "resolved": "Resolved", "closed": "Closed"}, "priority": {"all": "All priorities", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "departments": {"all": "All departments", "technical": "Technical", "billing": "Billing", "sales": "Sales", "general": "General"}, "empty": {"title": "No tickets found", "description": "No tickets match the search criteria"}, "not_found": {"title": "Ticket not found", "description": "The requested ticket does not exist or has been deleted."}, "details": {"main_info": "Main information", "id": "Number", "subject": "Subject", "status": "Status", "priority": "Priority", "department": "Department", "client": "Client", "assigned_to": "Assigned to", "created_at": "Created at", "updated_at": "Updated at", "last_reply": "Last reply", "service": "Associated service", "messages": "Messages", "internal_notes": "Internal notes", "attachments": "Attachments", "history": "History"}, "form": {"basic_info": "Basic information", "client": "Client", "select_client": "Select a client", "department": "Department", "select_department": "Select a department", "subject": "Subject", "subject_placeholder": "Ticket subject...", "priority_status": "Priority and status", "priority": "Priority", "status": "Status", "description": "Description", "assignment": "Assignment", "assigned_to": "Assigned to", "select_assignee": "Select an assignee", "unassigned": "Unassigned", "service_related": "Associated service", "service": "Associated service", "select_service": "Select a service", "no_service": "No service", "message": "Message", "message_placeholder": "Describe your request...", "advanced_options": "Advanced options", "internal_notes": "Internal notes", "internal_notes_placeholder": "Internal notes (not visible to client)...", "options": "Options", "notify_client": "Notify client", "notify_client_description": "Send email notification to client", "auto_close": "Auto-close", "auto_close_description": "Automatically close ticket after resolution", "tags_label": "Tags", "tags": "Tags", "tags_placeholder": "Add tags...", "notes": "Notes", "notes_placeholder": "Additional notes...", "notes_help": "Notes visible only to support team", "attachments": "Attachments"}, "reply": {"title": "Reply to ticket", "message": "Message", "message_placeholder": "Your reply...", "internal": "Internal note", "notify_client": "Notify client", "change_status": "Change status", "submit": "Send reply"}, "confirm_delete": "Are you sure you want to delete this ticket?", "confirm_close": "Are you sure you want to close this ticket?", "confirm_reopen": "Are you sure you want to reopen this ticket?", "success": {"created": "Ticket created successfully", "updated": "Ticket updated successfully", "replied": "Reply added successfully", "closed": "Ticket closed successfully", "reopened": "Ticket reopened successfully", "assigned": "Ticket assigned successfully"}, "errors": {"load_failed": "Error loading ticket", "create_failed": "Error creating ticket", "update_failed": "Error updating ticket", "reply_failed": "Error adding reply", "close_failed": "Error closing ticket", "reopen_failed": "Error reopening ticket", "assign_failed": "Error assigning ticket"}, "modal": {"add_title": "New ticket", "edit_title": "Edit ticket", "view_title": "Ticket details"}, "noMessages": "No messages"}, "settings": {"title": "Settings", "description": "System configuration", "loadError": "Error loading settings", "saveError": "Error saving settings", "saveSuccess": "Setting<PERSON> saved successfully", "general": {"title": "General settings", "description": "General system configuration", "siteName": "Site name", "siteUrl": "Site URL", "adminEmail": "Administrator email", "language": "Default language", "timezone": "Timezone", "dateFormat": "Date format", "timeFormat": "Time format", "currency": "<PERSON><PERSON><PERSON><PERSON>", "maintenanceMode": "Maintenance mode", "maintenanceMessage": "Maintenance message"}, "security": {"title": "Security", "description": "System security configuration", "authentication": "Authentication", "enableTwoFactor": "Enable two-factor authentication", "twoFactorHelp": "Adds an extra layer of security", "sessionTimeout": "Session timeout (minutes)", "maxLoginAttempts": "Maximum login attempts", "lockoutTime": "Lockout duration (minutes)", "passwordPolicy": "Password policy", "minPasswordLength": "Minimum password length", "requireUppercase": "Require uppercase", "requireNumber": "Require numbers", "requireSpecialChar": "Require special characters", "passwordExpiryDays": "Password expiry (days)", "apiAccess": "API Access", "enableApiAccess": "Enable API access", "apiTokenExpiry": "API token expiry (days)", "passwords": "Passwords", "api": "API", "never": "Never"}, "servers": {"online": "Online", "title": "Servers", "description": "Manage servers and modules", "add": "Add server", "edit": "Edit server", "delete": "Delete server", "name": "Name", "hostname": "Hostname", "type": "Type", "ip": "IP Address", "port": "Port", "username": "Username", "password": "Password", "passwordHint": "Leave empty if you don't want to modify", "apiToken": "API Token", "apiTokenHint": "Leave empty if you don't want to modify", "secure": "Secure connection (SSL/TLS)", "active": "Active", "maxAccounts": "Maximum number of accounts", "nameservers": "Nameservers (one per line)", "status": "Status", "noServers": "No servers configured", "confirmDelete": "Are you sure you want to delete this server?", "details": "Details", "refresh": "Refresh", "refreshing": "Refreshing...", "load": "CPU Load", "memory": "Memory", "disk": "Disk", "uptime": "Uptime", "info": "Information", "lastRefresh": "Last refresh", "lastCheck": "Last check", "last_updated": "Last updated", "apiTokenInputHint": "Leave empty if you don't want to modify", "nameserversHint": "One nameserver per line", "maxAccountsHint": "0 for unlimited", "fillRequiredFields": "Please fill in all required fields", "testConnection": "Test connection", "deleteConfirm": "Are you sure you want to delete server {name}?"}, "modules": {"title": "<PERSON><PERSON><PERSON>", "description": "Manage system modules", "searchPlaceholder": "Search for a module...", "filterByStatus": "Filter by status", "statusAll": "All", "statusActive": "Active", "statusInactive": "Inactive", "filterByType": "Module type", "typeAll": "All types", "types": {"servers": "Servers", "gateways": "Payment gateways", "addons": "Add-ons", "fraud": "Anti-fraud", "registrars": "Registrars", "reports": "Reports", "widgets": "Widgets"}, "by": "by", "active": "Active", "inactive": "Inactive", "noModulesFound": "No modules found", "noModulesMatchSearch": "No modules match your search", "noModulesMatchFilter": "No modules match the selected filter", "noModulesAvailable": "No modules available", "configTitle": "Module configuration", "noConfigAvailable": "No configuration available for this module", "loadError": "Error loading modules", "refreshSuccess": "<PERSON><PERSON><PERSON> refreshed successfully"}, "billing": {"title": "Billing", "description": "Configure billing settings and payment methods", "general": "General", "invoicePrefix": "Invoice prefix", "nextInvoiceNumber": "Next invoice number", "defaultDueDays": "Default due days", "autoSendInvoices": "Automatically send invoices", "autoSendReceipts": "Automatically send receipts", "taxes": "Taxes", "enableTaxes": "Enable taxes", "defaultTaxName": "Default tax name", "defaultTaxRate": "Default tax rate", "taxNumberRequired": "Tax number required", "paymentMethods": "Payment methods", "stripePublicKey": "Stripe public key", "stripeSecretKey": "Stripe secret key", "paypalClientId": "PayPal client ID", "paypalClientSecret": "PayPal client secret", "paypalSandbox": "PayPal sandbox mode", "bankDetails": "Bank details", "bankTransfer": "Bank transfer", "cash": "Cash"}, "integrations": {"title": "Integrations", "description": "Manage API integrations and webhooks", "apiKeys": "API Keys", "apiKeysDescription": "Manage API keys for external access to your system", "generateKey": "Generate new key", "newKeyGenerated": "New API key generated", "copyKeyWarning": "Copy this key now. It will not be shown again.", "keyName": "Key name", "createdAt": "Created at", "lastUsed": "Last used", "expiresAt": "Expires at", "noKeysFound": "No API keys found", "webhooks": "Webhooks", "webhooksDescription": "Configure webhooks to receive event notifications", "addWebhook": "Add webhook", "editWebhook": "Edit webhook", "webhookName": "Webhook name", "webhookUrl": "Webhook URL", "webhookEvents": "Events", "webhookStatus": "Status", "webhookActive": "Webhook active", "sendTestEvent": "Send test event", "noWebhooksFound": "No webhooks found", "events": {"clientCreated": "Client created", "clientUpdated": "Client updated", "invoiceCreated": "Invoice created", "invoicePaid": "Invoice paid", "paymentReceived": "Payment received", "ticketCreated": "Ticket created", "ticketReplied": "<PERSON><PERSON><PERSON> replied", "ticketClosed": "Ticket closed", "serviceCreated": "Service created", "serviceUpdated": "Service updated", "serviceExpired": "Service expired"}, "confirmDeleteKey": "Are you sure you want to delete this API key?", "confirmDeleteWebhook": "Are you sure you want to delete this webhook?", "keyGeneratedSuccess": "API key generated successfully", "keyGenerationError": "Error generating API key", "keyDeletedSuccess": "API key deleted successfully", "keyDeletionError": "Error deleting API key", "keyCopiedSuccess": "API key copied to clipboard", "keyCopyError": "Error copying API key", "webhookSavedSuccess": "Webhook saved successfully", "webhookSaveError": "Error saving webhook", "webhookDeletedSuccess": "Webhook deleted successfully", "webhookDeletionError": "Error deleting webhook", "webhookActivatedSuccess": "Webhook activated successfully", "webhookDeactivatedSuccess": "Webhook deactivated successfully"}, "license": {"title": "License", "description": "Manage system license", "information": "License information", "valid": "Valid license", "invalid": "Invalid license", "licenseKey": "License key", "registeredTo": "Registered to", "email": "Email", "plan": "Plan", "expiresAt": "Expires at", "supportUntil": "Support until", "noLicense": "No license configured", "update": "Update license", "activate": "Activate license", "check": "Check status", "features": {"multipleUsers": "Multiple users", "multipleUsersDesc": "Manage multiple admin users", "api": "API access", "apiDesc": "Full REST API access", "whiteLabel": "White label", "whiteLabelDesc": "Customize interface with your brand", "multipleServers": "Multiple servers", "multipleServersDesc": "Manage multiple provisioning servers", "advancedReporting": "Advanced reporting", "advancedReportingDesc": "Detailed reports and analytics", "prioritySupport": "Priority support", "prioritySupportDesc": "Priority technical support"}, "updateLicense": "Update license", "activateLicense": "Activate license", "licenseKeyHelp": "Enter your license key provided by TechCMS", "activationSuccess": "License activated successfully", "activationError": "Error activating license", "checkSuccess": "License status checked", "checkError": "Error checking license"}, "notifications": {"title": "Notifications", "description": "Configure system notifications", "email": "Email", "enableEmailNotifications": "Enable email notifications", "smtpHost": "SMTP host", "smtpPort": "SMTP port", "smtpSecurity": "SMTP security", "none": "None", "smtpUsername": "SMTP username", "smtpPassword": "SMTP password", "emailFrom": "From email", "emailFromName": "From name", "testConnection": "Test connection", "notificationTypes": "Notification types", "inApp": "In-app", "types": {"newClient": "New client", "newInvoice": "New invoice", "paymentReceived": "Payment received", "invoiceReminder": "Invoice reminder", "newTicket": "New ticket", "ticketReply": "Ticket reply", "serviceExpiry": "Service expiry", "systemAlert": "System alert"}, "testSuccess": "Email connection test successful", "testError": "Error testing email connection"}}, "subscriptions": {"title": "Subscriptions", "description": "Manage recurring subscriptions", "add_new": "New subscription", "create_title": "New subscription", "create_description": "Create a new subscription", "edit_title": "Edit subscription", "edit_description": "Edit subscription information", "details_title": "Subscription #{id}", "details_description": "Subscription details", "actions": {"add": "New subscription", "edit": "Edit", "cancel": "Cancel", "renew": "<PERSON>w", "view": "View"}, "search": {"placeholder": "Search by client, product..."}, "filters": {"search": "Search", "status": "Status", "product": "Product", "billing_cycle": "Billing cycle", "all_cycles": "All cycles", "all_time": "All time", "this_month": "This month", "last_month": "Last month", "this_year": "This year", "last_year": "Last year"}, "table": {"client": "Client", "product": "Product", "price": "Price", "cycle": "Cycle", "next_billing": "Next billing", "status": "Status", "actions": "Actions"}, "status": {"all": "All statuses", "active": "Active", "pending": "Pending", "cancelled": "Cancelled", "expired": "Expired", "suspended": "Suspended"}, "cycles": {"monthly": "Monthly", "quarterly": "Quarterly", "semi_annual": "Semi-annually", "annual": "Annually"}, "empty": {"title": "No subscriptions found", "description": "No subscriptions match the search criteria"}, "not_found": {"title": "Subscription not found", "description": "The requested subscription does not exist or has been deleted."}, "details": {"main_info": "Main information", "id": "Number", "status": "Status", "client": "Client", "product": "Product", "price": "Price", "billing_cycle": "Billing cycle", "start_date": "Start date", "end_date": "End date", "next_billing_date": "Next billing date", "auto_renew": "Auto-renewal", "server": "Server", "domain": "Domain", "username": "Username", "notes": "Notes", "history": "History", "billing_info": "Billing information", "service_info": "Service information"}, "form": {"client_product": "Client and product", "client": "Client", "select_client": "Select a client", "product": "Product", "select_product": "Select a product", "subscription_details": "Subscription details", "billing_info": "Billing information", "start_date": "Start date", "end_date": "End date", "billing_cycle": "Billing cycle", "next_billing": "Next billing", "next_billing_date": "Next billing date", "price": "Price", "status": "Status", "service_details": "Service details", "service_config": "Service configuration", "server": "Server", "select_server": "Select a server", "domain": "Domain", "domain_placeholder": "example.com", "username": "Username", "username_placeholder": "Service username", "advanced_options": "Advanced options", "options": "Options", "auto_renew": "Auto-renewal", "auto_renew_description": "Automatically renew this subscription when it expires", "send_notifications": "Send notifications", "send_notifications_description": "Send email notifications for this subscription", "notes": "Notes", "notes_placeholder": "Subscription notes..."}, "modal": {"add_title": "New subscription", "edit_title": "Edit subscription", "view_title": "Subscription details", "cancel_title": "Cancel subscription", "cancel_reason": "Cancellation reason", "cancel_reason_placeholder": "Why are you cancelling this subscription?", "confirm_cancel": "Are you sure you want to cancel this subscription?", "confirm_renew": "Are you sure you want to renew this subscription?"}, "history": {"created": "Subscription created", "updated": "Subscription updated", "cancelled": "Subscription cancelled", "renewed": "Subscription renewed", "suspended": "Subscription suspended", "reactivated": "Subscription reactivated"}, "success": {"created": "Subscription created successfully", "updated": "Subscription updated successfully", "cancelled": "Subscription cancelled successfully", "renewed": "Subscription renewed successfully"}, "errors": {"load_failed": "Error loading subscription", "create_failed": "Error creating subscription", "update_failed": "Error updating subscription", "cancel_failed": "Error cancelling subscription", "renew_failed": "Error renewing subscription"}}, "topbar": {"notifications": {"title": "Notifications", "toggle": "Toggle notifications", "mark_all_read": "Mark all as read", "markAllRead": "Mark all as read", "empty": "No notifications", "noNotifications": "No notifications", "view_all": "View all notifications"}, "profile": {"my_profile": "My profile", "system_updates": "System Updates", "settings": "Settings", "logout": "Logout"}}, "sidebar": {"menu": {"dashboard": "Dashboard", "clients": "Clients", "services": "Services", "products": "Products & Services", "catalog": {"title": "Catalog", "prod": "Products", "products_services": "Products & Services"}, "billing": {"title": "Billing", "invoices": "Invoices", "payments": "Payments", "subscriptions": "Subscriptions"}, "support": {"title": "Support", "tickets": "Tickets", "kb": "Knowledge base"}, "settings": "Settings"}}, "errors": {"realtime_connection": "Real-time connection error"}, "navigation": {"dashboard": "Dashboard", "clients": "Clients", "products": "Products & Services", "services": "Services", "billing": "Billing", "invoices": "Invoices", "payments": "Payments", "subscriptions": "Subscriptions", "support": "Support", "tickets": "Tickets", "settings": "Settings", "general": "General", "security": "Security", "servers": "Servers", "modules": "<PERSON><PERSON><PERSON>", "license": "License"}, "notifications": {"title": "Notifications", "description": "Manage system notifications", "markAllAsRead": "Mark all as read", "clearAll": "Clear all", "search": "Search notifications", "filterByType": "Filter by type", "markAsRead": "<PERSON> as read", "delete": "Delete", "stats": {"total": "Total", "unread": "Unread", "today": "Today"}, "status": {"all": "All", "unread": "Unread", "read": "Read"}, "types": {"ticket": "Ticket", "invoice": "Invoice", "payment": "Payment", "client": "Client", "system": "System"}, "columns": {"type": "Type", "title": "Title", "message": "Message", "date": "Date", "status": "Status"}, "empty": {"title": "No notifications", "description": "You have no notifications at the moment"}}, "updates": {"title": "System Updates", "description": "Manage TechCMS automatic system updates", "check_now": "Check Now", "settings": "Settings", "system_status": "System Status", "current_version": "Current Version", "available_updates": "Available Updates", "last_check": "Last Check", "last_update": "Last Update", "automation_status": "Automation Status", "auto_check": "Automatic Check", "auto_download": "Automatic Download", "auto_install": "Automatic Installation", "up_to_date": "Up to Date", "updates_available_count": "{count} update(s) available", "checking": "Checking...", "never": "Never", "just_now": "Just now", "hours_ago": "{count} hour(s) ago", "days_ago": "{count} day(s) ago", "download_latest": "Download Latest", "available_versions": "Available Versions", "version": "Version", "status": {"stable": "Stable", "beta": "Beta", "pending": "Pending", "downloading": "Downloading", "installing": "Installing", "completed": "Completed", "failed": "Failed", "rolled_back": "Rolled Back"}, "changelog": "Changelog", "download": "Download", "not_available": "Not Available", "history": "History", "cleanup": "Cleanup", "no_history": "No history", "date": "Date", "duration": "Duration", "size": "Size", "view_changelog": "View changelog", "view_error": "View error", "retry": "Retry", "filter_status": "Filter by status", "filter_version": "Filter by version", "filter_version_placeholder": "Search for a version...", "filter_date_from": "From date", "filter_date_to": "To date", "clear_filters": "Clear filters", "showing_results": "Showing {from} to {to} of {total} results", "changelog_title": "Changelog for version {version}", "no_changelog": "No changelog available", "error_details": "<PERSON><PERSON><PERSON>", "update_context": "Update Context", "started_at": "Started at", "failed_at": "Failed at", "check_success": "Check successful", "updates_found": "{count} update(s) found", "no_updates": "No updates available", "check_error": "Check error", "downloading_version": "Downloading version {version}", "download_success": "Download successful", "download_completed": "Download of version {version} completed", "download_error": "Download error", "cleanup_success": "Cleanup successful", "cleanup_completed": "{files} file(s) and {records} record(s) deleted", "cleanup_error": "Cleanup error", "settings_title": "Update Settings", "automatic_checking": "Automatic Checking", "enable_auto_check": "Enable automatic checking", "auto_check_description": "Automatically check for new versions at defined intervals", "check_interval": "Check interval", "check_interval_description": "Frequency of update checks", "interval_1h": "Every hour", "interval_6h": "Every 6 hours", "interval_12h": "Every 12 hours", "interval_24h": "Every 24 hours", "interval_48h": "Every 48 hours", "interval_7d": "Every week", "automatic_download": "Automatic Download", "enable_auto_download": "Enable automatic download", "auto_download_description": "Automatically download available updates", "requires_auto_check": "Requires automatic checking", "automatic_installation": "Automatic Installation", "enable_auto_install": "Enable automatic installation", "auto_install_description": "Automatically install downloaded updates", "requires_auto_download": "Requires automatic download", "backup_settings": "Backup Settings", "backup_before_update": "Backup before update", "backup_description": "Automatically create a backup before each update", "notification_settings": "Notification Settings", "notification_email": "Notification email", "notification_email_placeholder": "<EMAIL>", "notification_email_description": "Email address to receive update notifications", "configuration_preview": "Configuration Preview", "check_frequency": "Check frequency", "backup_enabled": "Backup enabled", "reset_defaults": "Reset to Defaults", "settings_saved": "Setting<PERSON> saved", "settings_updated": "Settings updated successfully", "settings_error": "Settings error", "invalid_email": "Invalid email format", "interval_too_short": "Interval must be at least 1 hour", "auto_install_requires_download": "Automatic installation requires automatic download", "auto_download_requires_check": "Automatic download requires automatic checking", "minutes": "{count} minute(s)", "hours": "{count} hour(s)", "days": "{count} day(s)", "preparing_download": "Preparing download...", "preparing_install": "Preparing installation...", "preparing": "Preparing...", "downloading": "Downloading...", "installing": "Installing...", "completed": "Completed", "failed": "Failed", "processing": "Processing...", "step_validate_token": "Token validation", "step_validate_token_desc": "Verifying download authorization", "step_download_file": "File download", "step_download_file_desc": "Downloading update from server", "step_verify_integrity": "Integrity verification", "step_verify_integrity_desc": "Checking downloaded file integrity", "step_complete": "Completion", "step_complete_desc": "Finalizing download", "step_backup": "Backup", "step_backup_desc": "Creating backup of current system", "step_extract": "Extraction", "step_extract_desc": "Extracting update files", "step_apply": "Application", "step_apply_desc": "Applying update to system", "step_cleanup": "Cleanup", "step_cleanup_desc": "Cleaning up temporary files", "version_details": "Version Details", "update_id": "Update ID", "unknown_error": "Unknown error", "install": "Install", "installing_version": "Installing version {version}", "install_success": "Installation successful", "install_completed": "Installation of version {version} completed", "install_error": "Installation error", "no_downloaded_version": "No downloaded version found", "requirements_not_met": "Requirements not met", "check_requirements_first": "Please check installation requirements first", "requirements_check_error": "Requirements check error", "rollback_success": "Rollback successful", "rollback_completed": "Rollback to version {version} completed", "rollback_error": "Rollback error", "no_backup_available": "No backup available", "confirm_rollback": "Are you sure you want to rollback to version {version}?", "backup_id": "Backup ID", "update_to_version": "Update to this version"}}
{"common": {"select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "view_all": "Voir tout", "client": "Client", "email": "Email", "status": "Statut", "date": "Date", "back": "Retour", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "create": "<PERSON><PERSON><PERSON>", "update": "Mettre à jour", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non", "name": "Nom", "description": "Description", "price": "Prix", "actions": "Actions", "retry": "<PERSON><PERSON><PERSON><PERSON>", "configure": "Configurer", "errorLoading": "<PERSON><PERSON><PERSON> lors du chargement", "close": "<PERSON><PERSON><PERSON>", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "active": "Actif", "inactive": "Inactif", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "minutes": "minutes", "minute": "minute", "hour": "heure", "hours": "heures", "day": "jour", "days": "jours", "confirmDelete": "Confirmer la <PERSON>", "activate": "Activer", "deactivate": "Désactiver", "never": "<PERSON><PERSON>", "saving": "Sauvegarde...", "error_occurred": "Une erreur s'est produite", "language": {"select": "Choisir la langue", "fr": "Français", "en": "English"}, "simple": "Simple", "advanced": "<PERSON><PERSON><PERSON>"}, "notFound": {"title": "404 - Page Non Trouvée", "description": "La page que vous recherchez n'existe pas ou a été déplacée.", "whatHappened": "Que s'est-il passé ?", "urlNotFound": "L'URL que vous avez saisie n'a pas été trouvée sur ce serveur.", "suggestions": "Suggestions :", "checkUrl": "Vérifiez que l'URL est correctement saisie", "goBack": "Retournez à la page précédente", "goToDashboard": "Accédez au tableau de bord", "backButton": "Retour", "dashboardButton": "Tableau de bord"}, "dashboard": {"title": "Tableau de bord", "subtitle": "Vue d'ensemble de votre activité", "refresh": "Actualiser", "viewMore": "Voir plus", "stats": {"active_clients": "Clients actifs", "active_services": "Services actifs", "active_tickets": "Tickets actifs", "monthly_revenue": "<PERSON><PERSON><PERSON> mensuels"}, "recent_clients": {"title": "Clients récents"}, "recent_tickets": {"title": "Tickets récents"}, "chart": {"today": "<PERSON><PERSON><PERSON>'hui", "week": "7 Jours", "month": "30 Jours", "year": "<PERSON><PERSON> an<PERSON>", "revenue": "<PERSON>en<PERSON>", "evolution": "Évolution des revenus"}}, "login": {"title": "Connexion", "email": "<PERSON><PERSON><PERSON> email", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié ?", "signIn": "Se connecter", "errorFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "resetTitle": "Réinitialiser le mot de passe", "resetDescription": "Entrez votre adresse email pour recevoir un lien de réinitialisation", "sendReset": "Envoyer le lien", "copyright": "© {year} TechCMS. Tous droits réservés."}, "clients": {"title": "Clients", "description": "<PERSON><PERSON>rer les clients et leurs informations", "add_new": "Nouveau client", "confirm_delete": "Êtes-vous sûr de vouloir supprimer ce client ?", "filters": {"search": "<PERSON><PERSON><PERSON>", "search_placeholder": "Rechercher par nom, email, entreprise...", "status": "Statut", "service_type": "Type de service"}, "table": {"client": "Client", "company": "Entreprise", "name": "Nom", "email": "Email", "phone": "Téléphone", "services": "Services", "status": "Statut", "created": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "status": {"all": "Tous les statuts", "active": "Actif", "inactive": "Inactif", "suspended": "Suspendu", "pending": "En attente"}, "services": {"all": "Tous les types", "hosting": "Hébergement", "domain": "Domaine", "ssl": "SSL"}, "no_services": "Aucun service", "actions": {"view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "add": {"title": "Nouveau client", "description": "<PERSON><PERSON>er un nouveau compte client"}, "edit": {"title": "Modifier le client", "description": "Modifier les informations du client"}, "details": {"title": "Détails du client", "description": "Informations détaillées du client", "error_loading": "Erreur lors du chargement des détails du client", "personal_info": "Informations personnelles", "contact_info": "Informations de contact", "billing_info": "Informations de facturation", "services_info": "Services", "notes_info": "Notes", "activity_info": "Activité récente", "no_services": "Aucun service actif", "no_notes": "Aucune note", "no_activity": "Aucune activité récente", "delete_confirm": "Êtes-vous sûr de vouloir supprimer ce client ?", "delete_warning": "Cette action est irréversible et supprimera toutes les données associées."}, "form": {"personal_info": "Informations personnelles", "contact_info": "Informations de contact", "billing_info": "Informations de facturation", "account_info": "Informations du compte", "address_title": "<PERSON><PERSON><PERSON>", "firstname": "Prénom", "firstname_placeholder": "Entrez le prénom", "lastname": "Nom", "lastname_placeholder": "Entrez le nom de famille", "email": "Email", "email_placeholder": "Entrez l'email", "phone": "Téléphone", "phone_placeholder": "+33 1 23 45 67 89", "company": "Entreprise", "company_placeholder": "Nom de l'entreprise (optionnel)", "address": "<PERSON><PERSON><PERSON>", "address_placeholder": "123 Rue de la Paix", "city": "Ville", "city_placeholder": "Paris", "state": "État/Province", "postal_code": "Code postal", "postal_code_placeholder": "75001", "postcode": "Code postal", "country": "Pays", "country_placeholder": "France", "password": "Mot de passe", "password_placeholder": "Mot de passe sécurisé", "password_optional": "(optionnel)", "password_leave_empty": "Laissez vide pour ne pas modifier", "confirm_password": "Confirmer le mot de passe", "status": "Statut", "notes": "Notes", "firstname_required": "Le prénom est requis", "lastname_required": "Le nom est requis", "email_required": "L'email est requis", "email_invalid": "Format d'email invalide", "phone_required": "Le téléphone est requis", "password_required": "Le mot de passe est requis", "password_min": "Le mot de passe doit contenir au moins 8 caractères", "password_mismatch": "Les mots de passe ne correspondent pas", "country_required": "Le pays est requis", "save": "Enregistrer", "saving": "Enregistrement...", "saved_successfully": "Client sauvegardé avec succès", "save_error": "<PERSON><PERSON><PERSON> lors de la sauvegarde du client"}, "empty": {"title": "Aucun client trouvé", "description": "Aucun client ne correspond aux critères de recherche"}}, "products_services": {"title": "Produits & Services", "description": "Gérer les produits et services disponibles", "add_new": "Nouveau produit", "create_title": "Créer un nouveau produit", "edit_title": "Modifier le produit", "type_selection_help": "Sélectionnez le type de produit que vous souhaitez créer. Chaque type a ses propres options de configuration.", "confirm_delete": "Êtes-vous sûr de vouloir supprimer ce produit ?", "filters": {"search": "<PERSON><PERSON><PERSON>", "type": "Type", "status": "Statut", "group": "Groupe", "price_range": "Gamme de prix", "all_types": "Tous les types", "all_prices": "Tous les prix", "price_low": "0€ - 50€", "price_medium": "50€ - 200€", "price_high": "200€ - 1000€"}, "types": {"reseller": "Hébergement revendeur", "dedicated": "<PERSON><PERSON><PERSON>", "vps": "VPS", "shared": "Hébergement partagé"}, "types_descriptions": {"shared_hosting": "Hébergement abordable et simple d'utilisation pour les petits sites web.", "reseller_hosting": "Hébergez vos propres clients avec nos plans revendeur.", "server_vps": "Un serveur privé virtuel pour plus de puissance et de flexibilité.", "domain": "Enregistrez ou transférez votre nom de domaine.", "other": "Autres types de produits et services."}, "table": {"name": "Nom", "type": "Type", "price": "Prix", "status": "Statut", "clients": "Clients", "actions": "Actions"}, "status": {"all": "Tous", "active": "Actif", "inactive": "Inactif", "maintenance": "Maintenance"}, "errors": {"fetch": "Erreur lors de la récupération des produits", "delete": "<PERSON><PERSON>ur lors de la suppression du produit", "load_failed": "Échec du chargement des produits", "modules_load": "Erreur lors du chargement des modules de serveurs", "type_required": "Veuillez sélectionner un type de produit", "save_failed": "<PERSON><PERSON><PERSON> lors de la sauvegarde", "name_required": "Le nom du produit est requis"}, "error_loading_data": "Erreur lors du chargement des données", "error_saving_data": "E<PERSON>ur lors de la sauvegarde des données", "no_products_found": "Aucun produit trouvé", "try_different_filter": "Essayez un filtre différent", "module": {"configuration": "Configuration du module", "loading_config": "Chargement de la configuration...", "select_plan": "Plan/Template", "select_plan_option": "Sélectionnez un plan", "operating_system": "Système d'exploitation", "select_os": "Sélectionnez un OS", "no_config_available": "Aucune configuration spécifique disponible pour ce module", "disk_space": "Espace disque", "disk_space_hint": "En MB (0 pour illimité)", "bandwidth": "Bande passante", "bandwidth_hint": "En MB (0 pour illimité)", "allow_subdomains": "Autoriser les sous-domaines", "allow_php": "Autoriser PHP", "max_email_accounts": "Comptes email maximum", "max_databases": "Bases de données maximum", "module_required": "Un module doit être sélectionné", "provision_option_required": "Une option de provisionnement doit être sélectionnée", "server_group_required": "Un groupe de serveurs doit être sélectionné", "saved_successfully": "Configuration du module sauvegardée avec succès", "cpanel_description": "Module de provisionnement pour cPanel/WHM", "plesk_description": "Module de provisionnement pour Plesk", "directadmin_description": "Module de provisionnement pour DirectAdmin", "proxmox_description": "Module de provisionnement pour Proxmox VE", "solusvm_description": "Module de provisionnement pour SolusVM", "tab_general": "Général", "tab_features": "Fonctionnalités", "tab_limits": "Limites", "description": "Configurez le module de provisionnement pour ce produit", "select_module": "Sélectionner un module", "select_module_placeholder": "Choisissez un module de provisionnement", "provision_options": "Options de provisionnement", "server_group": "Groupe de serveurs", "select_server_group": "Sélectionner un groupe de serveurs", "select_server": "S<PERSON><PERSON><PERSON><PERSON> un serveur", "select_server_is_required": "La sélection d'un serveur est requise.", "automation": "Automatisation", "auto_setup_on_order": "Configuration automatique à la commande", "auto_setup_on_payment": "Configuration automatique au paiement", "auto_setup_on_pending": "Configuration automatique en attente", "no_auto_setup": "Pas de configuration automatique", "welcome_email": "Email de bienvenue", "select_welcome_email": "Sélectionner un template d'email", "custom_module": "<PERSON><PERSON><PERSON>", "custom_description": "Configuration personnalisée du module", "provision_settings": "Paramètres de provisionnement", "auto_provision": "Provisionnement automatique", "auto_provision_description": "Provisionner automatiquement le service lors de la commande", "manual_provision": "Provisionnement manuel", "manual_provision_description": "Provisionner manuellement le service après la commande", "provisioning_type": "Type de provisionnement", "module_configuration": "Configuration du module", "auto_setup_trigger": "Déclencheur de configuration automatique", "enable_auto_provision": "Activer le provisionnement automatique", "package_name": "Nom du package", "package_help": "Sélectionnez un package/plan prédéfini pour ce module", "select_server_placeholder": "Choisissez un serveur", "server_help": "Sélectionnez le serveur qui hébergera ce service"}, "modules": {"provisioning_options": "Options de provisionnement"}, "tabs": {"type": "Type", "details": "Détails", "pricing": "Tarification", "module": "<PERSON><PERSON><PERSON>", "custom_fields": "<PERSON><PERSON>", "configurable_options": "Options configurables", "upgrades": "Mises à niveau", "freedomain": "<PERSON><PERSON> gratuit", "cross_sells": "Ventes croisées", "other": "Autres options", "links": "Liens utiles"}, "all_categories": "Toutes les catégories", "upgrades": {"description": "Configurez les mises à niveau disponibles pour ce produit", "select_products": "Sélectionner les produits", "no_products_selected": "Aucun produit sélectionné", "select_products_description": "Sélectionnez les produits vers lesquels les clients peuvent effectuer une mise à niveau"}, "details": {"description": "Description", "basic_info": "Informations de base", "name": "Nom du produit", "name_placeholder": "Entrez le nom du produit", "name_required": "Le nom du produit est requis", "slug": "Slug (URL)", "slug_placeholder": "slug-du-produit", "slug_hint": "Utilisé dans l'URL du produit. Laissez vide pour générer automatiquement.", "category": "<PERSON><PERSON><PERSON><PERSON>", "select_category": "Sélectionnez une catégorie", "category_required": "La catégorie est requise", "description_placeholder": "Description détaillée du produit", "short_description": "Description courte", "short_description_placeholder": "Résumé du produit", "display_options": "Options d'affichage", "welcome_email": "Email de bienvenue", "select_welcome_email": "Sélectionnez un email", "color": "<PERSON><PERSON><PERSON>", "image": "Image du produit", "remove_image": "Supprimer l'image", "hidden": "Produit ma<PERSON>", "hidden_hint": "Le produit ne sera pas visible dans la boutique", "featured": "Produit mis en avant", "featured_hint": "Le produit sera affiché en priorité", "additional_info": "Informations supplémentaires", "tag_line": "<PERSON><PERSON><PERSON>", "tag_line_placeholder": "<PERSON><PERSON><PERSON> acc<PERSON><PERSON>ur", "order_link": "Lien de commande person<PERSON><PERSON>", "order_link_placeholder": "https://example.com/order", "order_link_hint": "Lien externe pour commander ce produit", "saved_successfully": "Détails du produit sauvegardés avec succès"}, "columns": {"name": "Nom", "description": "Description", "type": "Type", "price": "Prix", "status": "Statut", "group": "Groupe", "clients": "Clients"}, "groups": {"all": "Tous les groupes", "none": "Aucun groupe"}, "actions": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "empty": {"title": "Aucun produit trouvé", "description": "Aucun produit ne correspond aux critères de recherche"}, "product_created_successfully": "Produit créé avec succès", "product_updated_successfully": "Produit mis à jour avec succès", "search": "Rechercher un produit...", "add": "Nouveau produit", "pricing": {"description": "Configurez les prix et options de facturation de votre produit", "setup_fee": "Frais d'installation", "setup_fee_hint": "<PERSON><PERSON> unique facturé lors de la première commande", "base_price": "Prix de base", "base_price_hint": "Prix principal du produit", "recurring_options": "Options de facturation récurrente", "recurring_pricing": "Facturation récurrente", "monthly": "<PERSON><PERSON><PERSON>", "quarterly": "<PERSON><PERSON><PERSON><PERSON>", "semiannually": "<PERSON><PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON>", "biennially": "<PERSON><PERSON><PERSON><PERSON>", "triennially": "<PERSON><PERSON><PERSON><PERSON>", "default_cycle": "Cycle de facturation par défaut", "allow_cycle_change": "Autoriser le changement de cycle", "allow_cycle_change_hint": "Permettre aux clients de changer leur cycle de facturation", "tax_options": "Options de taxation", "tax_included": "Taxes incluses", "tax_included_hint": "Les prix affichés incluent les taxes", "tax_exempt": "Exonéré de taxes", "tax_exempt_hint": "Ce produit est exonéré de taxes", "price_not_negative": "Les prix ne peuvent pas être négatifs", "saved_successfully": "Tarification sauvegardée avec succès", "billing_mode": "Mode de facturation", "single_cycle": "Cycle unique", "multiple_cycles": "Cycles multiples", "single_cycle_hint": "Le produit aura un seul cycle de facturation fixe", "multiple_cycles_hint": "Les clients pourront choisir parmi plusieurs cycles de facturation", "available_cycles": "Cycles disponibles", "select_cycles_hint": "Sélectionnez les cycles de facturation que vous souhaitez proposer aux clients", "billing_cycle": "Cycle de facturation", "price": "Prix", "set_default": "Définir par défaut", "default": "<PERSON><PERSON> <PERSON><PERSON>", "at_least_one_cycle": "Au moins un cycle de facturation doit être sélectionné", "at_least_one_price": "Au moins un cycle doit avoir un prix supérieur à 0"}, "custom_fields": {"description": "Configurez les champs personnalisés pour ce produit", "add_field": "Ajouter un champ", "field_name": "Nom du champ", "field_name_placeholder": "Nom du champ personnalisé", "field_type": "Type de champ", "field_description": "Description", "field_description_placeholder": "Description du champ", "required": "Requis", "admin_only": "Admin uniquement", "show_on_order": "<PERSON>ff<PERSON><PERSON> à la commande", "show_on_invoice": "Afficher sur la facture", "field_types": {"text": "Texte", "password": "Mot de passe", "dropdown": "Liste déroulante", "textarea": "Zone de texte", "checkbox": "Case à cocher", "radio": "Bouton radio"}, "options": "Options", "options_placeholder": "Option 1|Option 2|Option 3", "options_hint": "Séparez les options par |", "validation": "Validation", "regex_pattern": "Pattern regex", "error_message": "Message d'erreur", "saved_successfully": "Champs personnalisés sauvegardés avec succès", "no_fields": "Aucun champ <PERSON><PERSON><PERSON>", "no_fields_description": "Aucun champ personnalisé n'a été configuré pour ce produit"}, "configurable_options": {"description": "Configurez les options configurables pour ce produit", "add_option": "Ajouter une option", "option_name": "Nom de l'option", "option_name_placeholder": "Nom de l'option configurable", "option_type": "Type d'option", "option_description": "Description", "option_description_placeholder": "Description de l'option", "option_types": {"dropdown": "Liste déroulante", "radio": "Bouton radio", "checkbox": "Cases à cocher", "quantity": "Quantité", "slider": "<PERSON><PERSON>"}, "sub_options": "Sous-options", "add_sub_option": "Ajouter une sous-option", "sub_option_name": "Nom", "sub_option_price": "Prix", "sub_option_setup_fee": "Frais d'installation", "monthly_price": "Prix mensuel", "quarterly_price": "Prix trimestriel", "semiannual_price": "Prix semestriel", "annual_price": "Prix annuel", "min_quantity": "Quantité minimum", "max_quantity": "Quantité maximum", "saved_successfully": "Options configurables sauvegardées avec succès", "no_options": "Aucune option configurable", "no_options_description": "Aucune option configurable n'a été configurée pour ce produit"}, "freedomain": {"description": "Configurez les options de domaine gratuit pour ce produit", "enable_freedomain": "<PERSON><PERSON> le domaine gratuit", "enable_freedomain_hint": "Offrir un domaine gratuit avec ce produit", "domain_types": "Types de domaines", "register_domain": "Enregistrer un nouveau domaine", "transfer_domain": "Transférer un domaine existant", "subdomain": "Sous-domaine", "allowed_tlds": "Extensions autorisées", "allowed_tlds_placeholder": ".com,.net,.org", "allowed_tlds_hint": "Extensions de domaine autorisées, séparées par des virgules", "subdomain_options": "Options de sous-domaine", "subdomain_prefix": "Préfixe du sous-domaine", "subdomain_prefix_placeholder": "client", "subdomain_domain": "Domaine principal", "subdomain_domain_placeholder": "mondomaine.com", "saved_successfully": "Options de domaine gratuit sauvegardées avec succès"}, "cross_sells": {"description": "Configurez les produits de vente croisée pour ce produit", "add_product": "Ajouter un produit", "search_products": "Rechercher des produits", "selected_products": "Produits sélectionnés", "no_products_selected": "Aucun produit sélectionné", "product_name": "Nom du produit", "product_price": "Prix", "remove_product": "Retirer le produit", "saved_successfully": "Ventes croisées sauvegardées avec succès", "tab_cross_sells": "Ventes croisées", "tab_related": "Produits liés", "no_related": "Aucun produit lié", "no_related_description": "Aucun produit lié n'a été configuré pour ce produit", "no_cross_sells": "Aucune vente croisée", "no_cross_sells_description": "Aucune vente croisée n'a été configurée pour ce produit", "available_products": "Produits disponibles"}, "other": {"description": "Configurez les autres options pour ce produit", "configuration_title": "Configuration", "require_domain": "Domaine requis", "require_domain_help": "Un domaine est requis pour commander ce produit", "auto_setup": "Configuration automatique", "auto_setup_help": "Configurer automatiquement le service après la commande", "stock_control": "Contrôle du stock", "enable_stock_control": "Activer le contrôle du stock", "stock_quantity": "Quantité en stock", "stock_quantity_hint": "Nombre d'unités disponibles", "low_stock_threshold": "Seuil de stock faible", "low_stock_threshold_hint": "Alerte quand le stock atteint ce niveau", "stock_control_help": "G<PERSON>rer le stock disponible pour ce produit", "auto_provisioning": "Provisionnement automatique", "enable_auto_provisioning": "Activer le provisionnement automatique", "provisioning_delay": "<PERSON><PERSON><PERSON>", "provisioning_delay_hint": "<PERSON><PERSON><PERSON> en minutes avant le provisionnement", "retirement_options": "Options de retrait", "retirement_date": "Date de retrait", "retirement_date_hint": "Date à partir de laquelle le produit ne sera plus disponible", "replacement_product": "Produit de remplacement", "select_replacement": "Sélectionner un produit de remplacement", "hidden": "Produit ma<PERSON>", "hidden_help": "Masquer ce produit dans la boutique", "welcome_email": "Email de bienvenue", "welcome_email_none": "Aucun email", "welcome_email_help": "Email envoyé après l'activation du service", "notes": "Notes", "notes_placeholder": "Notes sur le produit...", "notes_help": "Notes internes sur ce produit", "saved_successfully": "Autres options sauvegardées avec succès"}, "links": {"description": "Configurez les liens utiles pour ce produit", "add_link": "Ajouter un lien", "link_title": "Titre du lien", "link_title_placeholder": "Documentation", "link_url": "URL du lien", "link_url_placeholder": "https://example.com/docs", "link_description": "Description", "link_description_placeholder": "Description du lien", "link_target": "Cible du lien", "target_self": "<PERSON><PERSON><PERSON> fenê<PERSON>", "target_blank": "Nouvelle fenêtre", "link_icon": "Icône", "link_icon_placeholder": "fas fa-book", "remove_link": "Supprimer le lien", "saved_successfully": "Liens utiles sauvegardés avec succès", "direct_links_title": "<PERSON><PERSON>", "direct_cart_link": "Lien panier direct", "direct_cart_link_placeholder": "ex: https://votreboutique.com/cart.php?a=add&pid=1", "direct_cart_link_template": "Template de lien panier", "direct_cart_link_template_placeholder": "ex: cart.php?a=add&pid={PRODUCT_ID}", "direct_cart_link_domain": "Domaine de lien panier", "direct_cart_link_domain_placeholder": "ex: votreboutique.com", "group_cart_link": "Lien panier de groupe", "group_cart_link_placeholder": "ex: https://votreboutique.com/cart.php?gid=1", "product_links_title": "Liens du produit", "url": "URL", "url_placeholder": "ex: https://example.com/produit/docs", "visits": "Visites", "actions": "Actions"}}, "product_groups": {"add": "Nouveau groupe", "success": {"create": "Groupe créé avec succès", "update": "Groupe mis à jour avec succès", "delete": "Groupe supprimé avec succès"}, "errors": {"create": "Erreur lors de la création du groupe", "update": "Erreur lors de la mise à jour du groupe", "delete": "<PERSON><PERSON>ur lors de la suppression du groupe"}, "confirm_delete": "Êtes-vous sûr de vouloir supprimer ce groupe ?"}, "services": {"title": "Services", "description": "Gérer les services clients", "add_new": "Nouveau service", "table": {"client": "Client", "product": "Produit", "domain": "Domaine", "status": "Statut", "next_due_date": "Prochaine éché<PERSON>", "recurring_amount": "<PERSON><PERSON>", "actions": "Actions"}, "status": {"all": "Tous les statuts", "pending": "En attente", "active": "Actif", "suspended": "Suspendu", "cancelled": "<PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON><PERSON><PERSON>", "fraud": "<PERSON><PERSON><PERSON>"}, "actions": {"view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "activate": "Activer", "suspend": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "terminate": "<PERSON><PERSON><PERSON>"}, "create": {"title": "Nouveau service", "description": "Créer un nouveau service client"}, "edit": {"title": "Modifier le service", "description": "Modifier les informations du service"}, "details": {"title": "Détails du service", "description": "Informations détaillées du service", "unknown_product": "Produit inconnu", "client": "Client", "client_name": "Nom du client", "client_email": "<PERSON><PERSON> du <PERSON>", "unknown": "Inconnu", "view_client": "Voir le client", "product": "Produit", "product_name": "Nom du produit", "view_product": "Voir le produit", "server": "Ser<PERSON><PERSON>", "server_name": "Nom du serveur", "view_server": "Voir le serveur", "no_server": "<PERSON><PERSON>n serveur assigné", "billing": "Facturation", "billing_cycle": "Cycle de facturation", "next_due_date": "Prochaine éché<PERSON>", "not_set": "Non défini", "recurring_amount": "<PERSON><PERSON>", "setup_fee": "Frais d'installation", "important_dates": "Dates importantes", "created_at": "<PERSON><PERSON><PERSON>", "suspension_date": "Date de suspension", "cancellation_date": "Date d'annulation", "termination_date": "Date de résiliation", "login_details": "Identifiants de connexion", "username": "Nom d'utilisateur", "password": "Mot de passe", "no_login_details": "Aucun identifiant de connexion", "configuration": "Configuration", "notes": "Notes", "error_loading": "Erreur lors du chargement du service"}, "filters": {"search": "<PERSON><PERSON><PERSON>", "search_placeholder": "Rechercher par client, produit, domaine...", "status": "Statut", "client": "Client", "product": "Produit", "all_clients": "Tous les clients", "all_products": "Tous les produits"}, "empty": {"title": "Aucun service trouvé", "message": "Aucun service ne correspond aux critères de recherche"}, "delete_modal": {"title": "Supprimer le service", "confirmation": "Êtes-vous sûr de vouloir supprimer ce service ?", "warning": "Cette action est irréversible et supprimera toutes les données associées."}, "status_change": {"activate_title": "Activer le service", "suspend_title": "Suspendre le service", "cancel_title": "Annuler le service", "terminate_title": "Terminer le service", "activate_confirmation": "Êtes-vous sûr de vouloir activer ce service ?", "suspend_confirmation": "Êtes-vous sûr de vouloir suspendre ce service ?", "cancel_confirmation": "Êtes-vous sûr de vouloir annuler ce service ?", "terminate_confirmation": "Êtes-vous sûr de vouloir terminer ce service ? Cette action est irréversible.", "notes": "Notes (optionnel)", "notes_placeholder": "Raison du changement de statut...", "success_title": "Statut modifié", "activate_success": "Le service a été activé avec succès", "suspend_success": "Le service a été suspendu avec succès", "cancel_success": "Le service a été annulé avec succès", "terminate_success": "Le service a été terminé avec succès", "error": "<PERSON><PERSON>ur lors du changement de statut", "invalid_action": "Action invalide"}, "billing_cycles": {"monthly": "<PERSON><PERSON><PERSON>", "quarterly": "<PERSON><PERSON><PERSON><PERSON>", "semi_annually": "<PERSON><PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON>", "biennially": "<PERSON><PERSON><PERSON><PERSON>", "triennially": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"section": {"client_product": "Client et produit", "service_details": "Détails du service", "details": "Détails du service", "billing": "Facturation", "configuration": "Configuration", "configurations": "Configurations", "notes": "Notes"}, "client": "Client", "select_client": "Sélectionner un client", "product": "Produit", "select_product": "Sélectionner un produit", "domain": "Domaine", "domain_placeholder": "exemple.com", "username": "Nom d'utilisateur", "username_placeholder": "Nom d'utilisateur du service", "password": "Mot de passe", "password_placeholder": "Mot de passe du service", "server": "Ser<PERSON><PERSON>", "select_server": "S<PERSON><PERSON><PERSON><PERSON> un serveur", "status": "Statut", "billing_cycle": "Cycle de facturation", "next_due_date": "Prochaine éché<PERSON>", "recurring_amount": "<PERSON><PERSON>", "amount_placeholder": "0.00", "setup_fee": "Frais d'installation", "fee_placeholder": "0.00", "notes": "Notes", "notes_placeholder": "Notes sur le service...", "config_name": "Nom de configuration", "config_value": "<PERSON><PERSON>", "action": "Action", "add_config": "Ajouter une configuration", "calculate_due_date": "Recalculer la date d'échéance", "error_loading": "Erreur lors du chargement des données", "error_submit": "Erreur lors de la soumission du formulaire", "success_create_title": "Service créé", "success_create_message": "Le service a été créé avec succès", "success_update_title": "Service mis à jour", "success_update_message": "Le service a été mis à jour avec succès", "errors": {"client_required": "Le client est requis", "product_required": "Le produit est requis", "status_required": "Le statut est requis", "invalid_date": "Format de date invalide", "negative_amount": "Le montant ne peut pas être négatif", "negative_fee": "Les frais ne peuvent pas être négatifs"}}}, "invoices": {"title": "Factures", "description": "G<PERSON>rer les factures et la facturation", "add_new": "Nouvelle facture", "create_title": "Nouvelle facture", "create_description": "<PERSON><PERSON>er une nouvelle facture", "edit_title": "Modifier la facture", "edit_description": "Modifier les informations de la facture", "details_title": "Facture #{id}", "details_description": "<PERSON>é<PERSON> de la facture", "actions": {"add": "Nouvelle facture", "edit": "Modifier", "mark_paid": "Marquer comme payée", "download_pdf": "Télécharger PDF"}, "search": {"placeholder": "Rechercher par numéro, client..."}, "filters": {"search": "<PERSON><PERSON><PERSON>", "status": "Statut", "date_range": "Période", "all_time": "Toutes les périodes", "this_month": "<PERSON> mois", "last_month": "Le mois dernier", "this_year": "<PERSON><PERSON> an<PERSON>", "last_year": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "table": {"number": "<PERSON><PERSON><PERSON><PERSON>", "client": "Client", "amount": "<PERSON><PERSON>", "status": "Statut", "date": "Date", "due_date": "Échéance", "actions": "Actions"}, "columns": {"service": "Service", "amount": "<PERSON><PERSON>", "due_date": "Date d'échéance", "status": "Statut", "paid_date": "Date de paiement", "created_at": "<PERSON><PERSON><PERSON>"}, "status": {"all": "Tous les statuts", "draft": "Brouillon", "unpaid": "Impayée", "paid": "Payée", "overdue": "En retard", "cancelled": "<PERSON><PERSON><PERSON>"}, "empty": {"title": "Aucune facture trouvée", "description": "Aucune facture ne correspond aux critères de recherche"}, "not_found": {"title": "Facture introuvable", "description": "La facture demandée n'existe pas ou a été supprimée."}, "details": {"main_info": "Informations principales", "id": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "date": "Date", "due_date": "Date d'échéance", "amount": "<PERSON><PERSON>", "client_info": "Informations client", "client": "Client", "email": "Email", "service": "Service", "items": "Éléments de facturation", "description": "Description", "quantity": "Quantité", "unit_price": "Prix unitaire", "total": "Total", "subtotal": "Sous-total", "tax": "Taxe", "payments": "Paiements associés", "notes": "Notes", "history": "Historique"}, "history": {"created": "Facture créée", "updated": "Facture modifiée", "paid": "Facture payée"}, "form": {"client_info": "Informations client", "client": "Client", "select_client": "Sélectionner un client", "service": "Service", "select_service": "Sélectionner un service", "invoice_details": "<PERSON>é<PERSON> de la facture", "date": "Date", "due_date": "Date d'échéance", "status": "Statut", "amount": "<PERSON><PERSON>", "items": "Éléments de facturation", "item_description": "Description", "item_description_placeholder": "Description de l'élément...", "quantity": "Quantité", "unit_price": "Prix unitaire", "total": "Total", "add_item": "Ajouter un élément", "notes": "Notes", "notes_placeholder": "Notes sur la facture...", "subtotal": "Sous-total", "tax": "Taxe"}, "modal": {"add_title": "Nouvelle facture", "edit_title": "Modifier la facture", "view_title": "<PERSON>é<PERSON> de la facture"}, "confirm_mark_paid": "Êtes-vous sûr de vouloir marquer cette facture comme payée ?", "success": {"created": "Facture créée avec succès", "updated": "Facture mise à jour avec succès", "marked_paid": "Facture marquée comme payée"}, "errors": {"load_failed": "Erreur lors du chargement de la facture", "create_failed": "E<PERSON>ur lors de la création de la facture", "update_failed": "Erreur lors de la mise à jour de la facture", "mark_paid_failed": "Erreur lors du marquage comme payée", "download_failed": "Erreur lors du téléchargement"}}, "payments": {"title": "Paiements", "description": "Gérer les paiements et transactions", "add_new": "Nouveau paiement", "create_title": "Nouveau paiement", "create_description": "Créer un nouveau paiement", "edit_title": "Modifier le paiement", "edit_description": "Modifier les informations du paiement", "details_title": "Paiement #{id}", "details_description": "Détails du paiement", "actions": {"add": "Nouveau paiement", "edit": "Modifier", "refund": "<PERSON><PERSON><PERSON><PERSON>"}, "search": {"placeholder": "Rechercher par transaction, client..."}, "filters": {"search": "<PERSON><PERSON><PERSON>", "status": "Statut", "method": "Méthode", "gateway": "<PERSON><PERSON><PERSON>", "date_range": "Période", "all_time": "Toutes les périodes", "this_month": "<PERSON> mois", "last_month": "Le mois dernier", "this_year": "<PERSON><PERSON> an<PERSON>", "last_year": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "table": {"transaction_id": "ID Transaction", "client": "Client", "invoice": "Facture", "amount": "<PERSON><PERSON>", "method": "Méthode", "gateway": "<PERSON><PERSON><PERSON>", "status": "Statut", "date": "Date", "actions": "Actions"}, "status": {"all": "Tous les statuts", "pending": "En attente", "completed": "Complété", "failed": "<PERSON><PERSON><PERSON>", "refunded": "Re<PERSON><PERSON><PERSON>"}, "methods": {"all": "Toutes les méthodes", "bank_transfer": "Virement bancaire", "cash": "Espèces"}, "empty": {"title": "Aucun paiement trouvé", "description": "Aucun paiement ne correspond aux critères de recherche"}, "not_found": {"title": "Paiement introuvable", "description": "Le paiement demandé n'existe pas ou a été supprimé."}, "details": {"main_info": "Informations principales", "id": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "amount": "<PERSON><PERSON>", "method": "Méthode", "date": "Date", "transaction_id": "ID Transaction", "client_invoice": "Client et facture", "client": "Client", "invoice": "Facture", "invoice_total": "Total facture", "notes": "Notes", "history": "Historique"}, "history": {"created": "<PERSON><PERSON><PERSON> c<PERSON>", "updated": "Paiement modifié", "refunded": "<PERSON><PERSON><PERSON> rembo<PERSON>"}, "form": {"basic_info": "Informations de base", "invoice": "Facture", "select_invoice": "Sélectionner une facture", "amount": "<PERSON><PERSON>", "method": "Méthode de paiement", "select_method": "Sélectionner une méthode", "status": "Statut", "additional_info": "Informations addition<PERSON>es", "transaction_id": "ID Transaction", "transaction_id_placeholder": "Identifiant de la transaction...", "notes": "Notes", "notes_placeholder": "Notes sur le paiement..."}, "modal": {"add_title": "Nouveau paiement", "edit_title": "Modifier le paiement", "view_title": "Détails du paiement"}, "confirm_refund": "Êtes-vous sûr de vouloir rembourser ce paiement ?", "success": {"created": "Paiement créé avec succès", "updated": "Paiement mis à jour avec succès", "refunded": "Paiement remboursé avec succès"}, "errors": {"load_failed": "Erreur lors du chargement du paiement", "create_failed": "<PERSON><PERSON>ur lors de la création du paiement", "update_failed": "Erreur lors de la mise à jour du paiement", "refund_failed": "<PERSON><PERSON><PERSON> lors du remboursement"}}, "tickets": {"title": "Tickets de support", "description": "<PERSON><PERSON><PERSON> les demandes de support client", "add_new": "Nouveau ticket", "create_title": "Nouveau ticket", "create_description": "<PERSON><PERSON>er un nouveau ticket de support", "edit_title": "Modifier le ticket", "edit_description": "Modifier les informations du ticket", "details_title": "Ticket #{id}", "details_description": "Détails du ticket de support", "actions": {"add": "Nouveau ticket", "create": "<PERSON><PERSON><PERSON> le ticket", "update": "Mettre à jour", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "close": "<PERSON><PERSON><PERSON>", "reopen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assign": "Assigner"}, "filters": {"search": "<PERSON><PERSON><PERSON>", "status": "Statut", "priority": "Priorité", "department": "Département", "assigned_to": "<PERSON><PERSON><PERSON>", "all_time": "Toutes les périodes", "this_month": "<PERSON> mois", "last_month": "Le mois dernier", "this_year": "<PERSON><PERSON> an<PERSON>", "last_year": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "search": {"placeholder": "Rechercher par sujet, client..."}, "table": {"subject": "Sujet", "client": "Client", "status": "Statut", "priority": "Priorité", "department": "Département", "assigned_to": "<PERSON><PERSON><PERSON>", "last_reply": "Dernière réponse", "created_at": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "actions": "Actions"}, "status": {"all": "Tous les statuts", "open": "Ouvert", "pending": "En attente", "answered": "Répondu", "customer-reply": "Réponse client", "resolved": "R<PERSON>ol<PERSON>", "closed": "<PERSON><PERSON><PERSON>"}, "priority": {"all": "Toutes les priorités", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "urgent": "<PERSON><PERSON>"}, "departments": {"all": "Tous les départements", "technical": "Technique", "billing": "Facturation", "sales": "<PERSON><PERSON><PERSON>", "general": "Général"}, "empty": {"title": "Aucun ticket trouvé", "description": "Aucun ticket ne correspond aux critères de recherche"}, "not_found": {"title": "Ticket introuvable", "description": "Le ticket demandé n'existe pas ou a été supprimé."}, "details": {"main_info": "Informations principales", "id": "<PERSON><PERSON><PERSON><PERSON>", "subject": "Sujet", "status": "Statut", "priority": "Priorité", "department": "Département", "client": "Client", "assigned_to": "<PERSON><PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON>", "updated_at": "Mis à jour le", "last_reply": "Dernière réponse", "service": "Service associé", "messages": "Messages", "internal_notes": "Notes internes", "attachments": "Pièces jointes", "history": "Historique"}, "form": {"basic_info": "Informations de base", "client": "Client", "select_client": "Sélectionner un client", "department": "Département", "select_department": "Sélectionner un département", "subject": "Sujet", "subject_placeholder": "Sujet du ticket...", "priority_status": "Priorité et statut", "priority": "Priorité", "status": "Statut", "description": "Description", "assignment": "Assignation", "assigned_to": "<PERSON><PERSON><PERSON>", "select_assignee": "Sélectionner un assigné", "unassigned": "Non assigné", "service_related": "Service associé", "service": "Service associé", "select_service": "Sélectionner un service", "no_service": "Aucun service", "message": "Message", "message_placeholder": "Décrivez votre demande...", "advanced_options": "Options avancées", "internal_notes": "Notes internes", "internal_notes_placeholder": "Notes internes (non visibles par le client)...", "options": "Options", "notify_client": "Notifier le client", "notify_client_description": "Envoyer une notification par email au client", "auto_close": "Fermeture automatique", "auto_close_description": "Fermer automatiquement le ticket après résolution", "tags_label": "Tags", "tags": "Tags", "tags_placeholder": "Ajouter des tags...", "notes": "Notes", "notes_placeholder": "Notes additionnelles...", "notes_help": "Notes visibles uniquement par l'équipe de support", "attachments": "Pièces jointes"}, "reply": {"title": "Répondre au ticket", "message": "Message", "message_placeholder": "Votre réponse...", "internal": "Note interne", "notify_client": "Notifier le client", "change_status": "Changer le statut", "submit": "Envoyer la réponse"}, "confirm_delete": "Êtes-vous sûr de vouloir supprimer ce ticket ?", "confirm_close": "Êtes-vous sûr de vouloir fermer ce ticket ?", "confirm_reopen": "Êtes-vous sûr de vouloir rouvrir ce ticket ?", "success": {"created": "Ticket créé avec succès", "updated": "Ticket mis à jour avec succès", "replied": "Réponse ajoutée avec succès", "closed": "Ticket fermé avec succès", "reopened": "Ticket rouvert avec succès", "assigned": "Ticket assigné avec succès"}, "errors": {"load_failed": "Erreur lors du chargement du ticket", "create_failed": "Erreur lors de la création du ticket", "update_failed": "Erreur lors de la mise à jour du ticket", "reply_failed": "Erreur lors de l'ajout de la réponse", "close_failed": "Erreur lors de la fermeture du ticket", "reopen_failed": "Erreur lors de la réouverture du ticket", "assign_failed": "Erreur lors de l'assignation du ticket"}, "modal": {"add_title": "Nouveau ticket", "edit_title": "Modifier le ticket", "view_title": "<PERSON>é<PERSON> du ticket"}, "noMessages": "Aucun message"}, "settings": {"title": "Paramètres", "description": "Configuration du système", "loadError": "Erreur lors du chargement des paramètres", "saveError": "Erreur lors de la sauvegarde des paramètres", "saveSuccess": "Paramètres sauvegardés avec succès", "general": {"title": "Paramètres généraux", "description": "Configuration générale du système", "siteName": "Nom du site", "siteUrl": "URL du site", "adminEmail": "Email administrateur", "language": "Langue par défaut", "timezone": "<PERSON><PERSON> ho<PERSON>", "dateFormat": "Format de date", "timeFormat": "Format d'heure", "currency": "<PERSON><PERSON>", "maintenanceMode": "Mode maintenance", "maintenanceMessage": "Message de maintenance"}, "security": {"title": "Sécurité", "description": "Configuration de la sécurité du système", "authentication": "Authentification", "enableTwoFactor": "Activer l'authentification à deux facteurs", "twoFactorHelp": "Ajoute une couche de sécurité supplémentaire", "sessionTimeout": "<PERSON><PERSON><PERSON> d'expiration de session (minutes)", "maxLoginAttempts": "Tentatives de connexion maximum", "lockoutTime": "<PERSON><PERSON><PERSON> verrouillage (minutes)", "passwordPolicy": "Politique de mot de passe", "minPasswordLength": "Longueur minimum du mot de passe", "requireUppercase": "Exiger des majuscules", "requireNumber": "Exiger des chiffres", "requireSpecialChar": "Exiger des caractères spéciaux", "passwordExpiryDays": "Expiration du mot de passe (jours)", "apiAccess": "Accès API", "enableApiAccess": "Activer l'accès API", "apiTokenExpiry": "Expiration du token API (jours)", "passwords": "Mots de passe", "api": "API", "never": "<PERSON><PERSON>"}, "servers": {"online": "En ligne", "title": "Serveurs", "description": "<PERSON><PERSON><PERSON> les serveurs et modules", "add": "Ajouter un serveur", "edit": "Modifier le serveur", "delete": "<PERSON><PERSON><PERSON><PERSON> le serveur", "name": "Nom", "hostname": "Nom d'hôte", "type": "Type", "ip": "Adresse IP", "port": "Port", "username": "Nom d'utilisateur", "password": "Mot de passe", "passwordHint": "Laissez vide si vous ne souhaitez pas modifier", "apiToken": "Token API", "apiTokenHint": "Laissez vide si vous ne souhaitez pas modifier", "secure": "Connexion sécurisée (SSL/TLS)", "active": "Actif", "maxAccounts": "Nombre maximum de comptes", "nameservers": "Serveurs de noms (un par ligne)", "status": "Statut", "noServers": "<PERSON><PERSON>n serveur configuré", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce serveur ?", "details": "Détails", "refresh": "Actualiser", "refreshing": "Actualisation en cours...", "load": "Charge CPU", "memory": "M<PERSON><PERSON><PERSON>", "disk": "Disque", "uptime": "Temps de fonctionnement", "info": "Informations", "lastRefresh": "Dernière actualisation", "lastCheck": "Dernière vérification", "last_updated": "Dernière mise à jour", "apiTokenInputHint": "Laissez vide si vous ne souhaitez pas modifier", "nameserversHint": "Un serveur de noms par ligne", "maxAccountsHint": "0 pour illimité", "fillRequiredFields": "Veuillez remplir tous les champs requis", "testConnection": "Tester la connexion", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer le serveur {name} ?"}, "modules": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> les modules du système", "searchPlaceholder": "Rechercher un module...", "filterByStatus": "Filtrer par statut", "statusAll": "Tous", "statusActive": "Actifs", "statusInactive": "Inactifs", "filterByType": "Type de module", "typeAll": "Tous les types", "types": {"servers": "Serveurs", "gateways": "Passerelles de paiement", "addons": "Extensions", "fraud": "Anti-fraude", "registrars": "Registraires", "reports": "Rapports", "widgets": "Widgets"}, "by": "par", "active": "Actif", "inactive": "Inactif", "noModulesFound": "Aucun module trouvé", "noModulesMatchSearch": "Aucun module ne correspond à votre recherche", "noModulesMatchFilter": "Aucun module ne correspond au filtre sélectionné", "noModulesAvailable": "Aucun module disponible", "configTitle": "Configuration du module", "noConfigAvailable": "Aucune configuration disponible pour ce module", "loadError": "Erreur lors du chargement des modules", "refreshSuccess": "Modules actualisés avec succès"}, "billing": {"title": "Facturation", "description": "Configurez les paramètres de facturation et les méthodes de paiement", "general": "Général", "invoicePrefix": "Préfixe des factures", "nextInvoiceNumber": "Prochain numéro de facture", "defaultDueDays": "<PERSON><PERSON><PERSON> de paiement par défaut (jours)", "autoSendInvoices": "Envoyer automatiquement les factures", "autoSendReceipts": "Envoyer automatiquement les reçus", "taxes": "Taxes", "enableTaxes": "Activer les taxes", "defaultTaxName": "Nom de la taxe par défaut", "defaultTaxRate": "Taux de taxe par défaut", "taxNumberRequired": "Numéro de taxe requis", "paymentMethods": "Méthodes de paiement", "stripePublicKey": "Clé publique Stripe", "stripeSecretKey": "Clé secrète Stripe", "paypalClientId": "ID client PayPal", "paypalClientSecret": "Secret client PayPal", "paypalSandbox": "Mode sandbox PayPal", "bankDetails": "Détails ban<PERSON>ires", "bankTransfer": "Virement bancaire", "cash": "Espèces"}, "integrations": {"title": "Intégrations", "description": "<PERSON><PERSON>rez les intégrations API et les webhooks", "apiKeys": "Clés API", "apiKeysDescription": "<PERSON><PERSON><PERSON> les clés API pour l'accès externe à votre système", "generateKey": "Générer une nouvelle clé", "newKeyGenerated": "Nouvelle clé API générée", "copyKeyWarning": "<PERSON><PERSON><PERSON> cette clé maintenant. Elle ne sera plus affichée.", "keyName": "Nom de la clé", "createdAt": "<PERSON><PERSON><PERSON>", "lastUsed": "Dernière utilisation", "expiresAt": "Expire le", "noKeysFound": "Aucune clé API trouvée", "webhooks": "Webhooks", "webhooksDescription": "Configurez les webhooks pour recevoir des notifications d'événements", "addWebhook": "Ajouter un webhook", "editWebhook": "Modifier le webhook", "webhookName": "Nom du webhook", "webhookUrl": "URL du webhook", "webhookEvents": "Événements", "webhookStatus": "Statut", "webhookActive": "Webhook actif", "sendTestEvent": "Envoyer un événement de test", "noWebhooksFound": "Aucun webhook trouvé", "events": {"clientCreated": "Client c<PERSON>", "clientUpdated": "Client mis à jour", "invoiceCreated": "Facture créée", "invoicePaid": "Facture payée", "paymentReceived": "Pa<PERSON><PERSON> reçu", "ticketCreated": "Ticket cré<PERSON>", "ticketReplied": "Réponse au ticket", "ticketClosed": "Ticket fermé", "serviceCreated": "Service créé", "serviceUpdated": "Service mis à jour", "serviceExpired": "Service expiré"}, "confirmDeleteKey": "Êtes-vous sûr de vouloir supprimer cette clé API ?", "confirmDeleteWebhook": "Êtes-vous sûr de vouloir supprimer ce webhook ?", "keyGeneratedSuccess": "Clé API générée avec succès", "keyGenerationError": "Erreur lors de la génération de la clé API", "keyDeletedSuccess": "Clé API supprimée avec succès", "keyDeletionError": "Erreur lors de la suppression de la clé API", "keyCopiedSuccess": "Clé API copiée dans le presse-papiers", "keyCopyError": "Erreur lors de la copie de la clé API", "webhookSavedSuccess": "Webhook sauvegardé avec succès", "webhookSaveError": "<PERSON><PERSON>ur lors de la sauvegarde du webhook", "webhookDeletedSuccess": "Webhook supprimé avec succès", "webhookDeletionError": "<PERSON><PERSON><PERSON> lors de la suppression du webhook", "webhookActivatedSuccess": "Webhook activé avec succès", "webhookDeactivatedSuccess": "Webhook désactivé avec succès"}, "license": {"title": "Licence", "description": "<PERSON><PERSON><PERSON> la licence du système", "information": "Informations sur la licence", "valid": "Licence valide", "invalid": "Licence invalide", "licenseKey": "Clé de licence", "registeredTo": "Enre<PERSON><PERSON><PERSON> à", "email": "Email", "plan": "Plan", "expiresAt": "Expire le", "supportUntil": "Support jusqu'au", "noLicense": "Aucune licence configurée", "update": "Mettre à jour la licence", "activate": "Activer la <PERSON>", "check": "Vérifier le statut", "features": {"multipleUsers": "Utilisateurs multiples", "multipleUsersDesc": "<PERSON><PERSON><PERSON> plusieurs utilisateurs administrateurs", "api": "Accès API", "apiDesc": "Accès complet à l'API REST", "whiteLabel": "<PERSON><PERSON> blanche", "whiteLabelDesc": "Personnaliser l'interface avec votre marque", "multipleServers": "Serveurs multiples", "multipleServersDesc": "<PERSON><PERSON><PERSON> plusieurs serveurs de provisioning", "advancedReporting": "Rapports avancés", "advancedReportingDesc": "Rapports détaillés et analytics", "prioritySupport": "Support prioritaire", "prioritySupportDesc": "Support technique prioritaire"}, "updateLicense": "Mettre à jour la licence", "activateLicense": "Activer la <PERSON>", "licenseKeyHelp": "Entrez votre clé de licence fournie par TechCMS", "activationSuccess": "Licence activée avec succès", "activationError": "Erreur lors de l'activation de la licence", "checkSuccess": "Statut de la licence vérifié", "checkError": "Erreur lors de la vérification de la licence"}, "notifications": {"title": "Notifications", "description": "Configurer les notifications système", "email": "Email", "enableEmailNotifications": "Activer les notifications par email", "smtpHost": "Serveur SMTP", "smtpPort": "Port SMTP", "smtpSecurity": "Sécurité SMTP", "none": "Aucune", "smtpUsername": "Nom d'utilisateur SMTP", "smtpPassword": "Mot de passe SMTP", "emailFrom": "<PERSON>ail expéditeur", "emailFromName": "Nom expéditeur", "testConnection": "Tester la connexion", "notificationTypes": "Types de notifications", "inApp": "Dans l'application", "types": {"newClient": "Nouveau client", "newInvoice": "Nouvelle facture", "paymentReceived": "Pa<PERSON><PERSON> reçu", "invoiceReminder": "Rappel de facture", "newTicket": "Nouveau ticket", "ticketReply": "Réponse au ticket", "serviceExpiry": "Expiration de service", "systemAlert": "Alerte système"}, "testSuccess": "Test de connexion email r<PERSON><PERSON>i", "testError": "Erreur lors du test de connexion email"}}, "subscriptions": {"title": "Abonnements", "description": "<PERSON><PERSON><PERSON> les abonnements récurrents", "add_new": "Nouvel abonnement", "create_title": "Nouvel abonnement", "create_description": "Créer un nouvel abonnement", "edit_title": "Modifier l'abonnement", "edit_description": "Modifier les informations de l'abonnement", "details_title": "Abonnement #{id}", "details_description": "Détails de l'abonnement", "actions": {"add": "Nouvel abonnement", "edit": "Modifier", "cancel": "Annuler", "renew": "Renouveler", "view": "Voir"}, "search": {"placeholder": "Rechercher par client, produit..."}, "filters": {"search": "<PERSON><PERSON><PERSON>", "status": "Statut", "product": "Produit", "billing_cycle": "Cycle de facturation", "all_cycles": "Tous les cycles", "all_time": "Toutes les périodes", "this_month": "<PERSON> mois", "last_month": "Le mois dernier", "this_year": "<PERSON><PERSON> an<PERSON>", "last_year": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "table": {"client": "Client", "product": "Produit", "price": "Prix", "cycle": "Cycle", "next_billing": "Prochaine facturation", "status": "Statut", "actions": "Actions"}, "status": {"all": "Tous les statuts", "active": "Actif", "pending": "En attente", "cancelled": "<PERSON><PERSON><PERSON>", "expired": "Expiré", "suspended": "Suspendu"}, "cycles": {"monthly": "<PERSON><PERSON><PERSON>", "quarterly": "<PERSON><PERSON><PERSON><PERSON>", "semi_annual": "<PERSON><PERSON><PERSON><PERSON>", "annual": "<PERSON><PERSON>"}, "empty": {"title": "Aucun abonnement trouvé", "description": "Aucun abonnement ne correspond aux critères de recherche"}, "not_found": {"title": "Abonnement introuvable", "description": "L'abonnement demandé n'existe pas ou a été supprimé."}, "details": {"main_info": "Informations principales", "id": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "client": "Client", "product": "Produit", "price": "Prix", "billing_cycle": "Cycle de facturation", "start_date": "Date de début", "end_date": "Date de fin", "next_billing_date": "Prochaine facturation", "auto_renew": "Renouvellement automatique", "server": "Ser<PERSON><PERSON>", "domain": "Domaine", "username": "Nom d'utilisateur", "notes": "Notes", "history": "Historique", "billing_info": "Informations de facturation", "service_info": "Informations du service"}, "form": {"client_product": "Client et produit", "client": "Client", "select_client": "Sélectionner un client", "product": "Produit", "select_product": "Sélectionner un produit", "subscription_details": "Détails de l'abonnement", "billing_info": "Informations de facturation", "start_date": "Date de début", "end_date": "Date de fin", "billing_cycle": "Cycle de facturation", "next_billing": "Prochaine facturation", "next_billing_date": "Prochaine facturation", "price": "Prix", "status": "Statut", "service_details": "Détails du service", "service_config": "Configuration du service", "server": "Ser<PERSON><PERSON>", "select_server": "S<PERSON><PERSON><PERSON><PERSON> un serveur", "domain": "Domaine", "domain_placeholder": "exemple.com", "username": "Nom d'utilisateur", "username_placeholder": "Nom d'utilisateur du service", "advanced_options": "Options avancées", "options": "Options", "auto_renew": "Renouvellement automatique", "auto_renew_description": "Renouveler automatiquement cet abonnement à l'expiration", "send_notifications": "Envoyer des notifications", "send_notifications_description": "Envoyer des notifications par email pour cet abonnement", "notes": "Notes", "notes_placeholder": "Notes sur l'abonnement..."}, "modal": {"add_title": "Nouvel abonnement", "edit_title": "Modifier l'abonnement", "view_title": "Détails de l'abonnement", "cancel_title": "Annuler l'abonnement", "cancel_reason": "Raison de l'annulation", "cancel_reason_placeholder": "Pourquoi annulez-vous cet abonnement ?", "confirm_cancel": "Êtes-vous sûr de vouloir annuler cet abonnement ?", "confirm_renew": "Êtes-vous sûr de vouloir renouveler cet abonnement ?"}, "history": {"created": "Abonnement créé", "updated": "Abonnement modifié", "cancelled": "Abonnement annulé", "renewed": "Abonnement renouvelé", "suspended": "Abonnement suspendu", "reactivated": "Abonnement réactivé"}, "success": {"created": "Abonnement créé avec succès", "updated": "Abonnement mis à jour avec succès", "cancelled": "Abonnement annulé avec succès", "renewed": "Abonnement renouvelé avec succès"}, "errors": {"load_failed": "Erreur lors du chargement de l'abonnement", "create_failed": "Erreur lors de la création de l'abonnement", "update_failed": "Erreur lors de la mise à jour de l'abonnement", "cancel_failed": "Erreur lors de l'annulation de l'abonnement", "renew_failed": "Erreur lors du renouvellement de l'abonnement"}}, "topbar": {"notifications": {"title": "Notifications", "toggle": "Basculer les notifications", "mark_all_read": "<PERSON><PERSON> tout comme lu", "markAllRead": "<PERSON><PERSON> tout comme lu", "empty": "Aucune notification", "noNotifications": "Aucune notification", "view_all": "Voir toutes les notifications"}, "profile": {"my_profile": "Mon profil", "system_updates": "<PERSON>ses à jour", "settings": "Paramètres", "logout": "Déconnexion"}}, "sidebar": {"menu": {"dashboard": "Tableau de bord", "clients": "Clients", "services": "Services", "products": "Produits & Services", "catalog": {"title": "Catalogue", "prod": "Produits", "products_services": "Produits & Services"}, "billing": {"title": "Facturation", "invoices": "Factures", "payments": "Paiements", "subscriptions": "Abonnements"}, "support": {"title": "Support", "tickets": "Tickets", "kb": "Base de connaissances"}, "settings": "Paramètres"}}, "errors": {"realtime_connection": "Erreur de connexion temps réel"}, "navigation": {"dashboard": "Tableau de bord", "clients": "Clients", "products": "Produits & Services", "services": "Services", "billing": "Facturation", "invoices": "Factures", "payments": "Paiements", "subscriptions": "Abonnements", "support": "Support", "tickets": "Tickets", "settings": "Paramètres", "general": "Général", "security": "Sécurité", "servers": "Serveurs", "modules": "<PERSON><PERSON><PERSON>", "license": "Licence"}, "notifications": {"title": "Notifications", "description": "<PERSON><PERSON><PERSON> les notifications système", "markAllAsRead": "<PERSON><PERSON> tout comme lu", "clearAll": "Tout effacer", "search": "Rechercher dans les notifications", "filterByType": "Filtrer par type", "markAsRead": "Marquer comme lu", "delete": "<PERSON><PERSON><PERSON><PERSON>", "stats": {"total": "Total", "unread": "Non lues", "today": "<PERSON><PERSON><PERSON>'hui"}, "status": {"all": "Toutes", "unread": "Non lues", "read": "<PERSON><PERSON>"}, "types": {"ticket": "Ticket", "invoice": "Facture", "payment": "Paiement", "client": "Client", "system": "Système"}, "columns": {"type": "Type", "title": "Titre", "message": "Message", "date": "Date", "status": "Statut"}, "empty": {"title": "Aucune notification", "description": "Vous n'avez aucune notification pour le moment"}}, "updates": {"title": "Mises à jour système", "description": "<PERSON><PERSON><PERSON> les mises à jour automatiques du système TechCMS", "check_now": "Vérifier maintenant", "settings": "Paramètres", "system_status": "Statut du système", "current_version": "Version actuelle", "available_updates": "Mises à jour disponibles", "last_check": "Dernière vérification", "last_update": "Dernière mise à jour", "automation_status": "Statut de l'automatisation", "auto_check": "Vérification automatique", "auto_download": "Téléchargement automatique", "auto_install": "Installation automatique", "up_to_date": "À jour", "updates_available_count": "{count} mise(s) à jour disponible(s)", "checking": "Vérification en cours...", "never": "<PERSON><PERSON>", "just_now": "À l'instant", "hours_ago": "{count} heure(s)", "days_ago": "{count} jour(s)", "download_latest": "Télécharger la dernière", "available_versions": "Versions disponibles", "version": "Version", "status": {"stable": "Stable", "beta": "<PERSON><PERSON><PERSON>", "pending": "En attente", "downloading": "Téléchargement", "installing": "Installation", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "rolled_back": "<PERSON><PERSON><PERSON>"}, "changelog": "Notes de version", "download": "Télécharger", "not_available": "Non disponible", "history": "Historique", "cleanup": "<PERSON><PERSON><PERSON>", "no_history": "Aucun historique", "date": "Date", "duration": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "view_changelog": "Voir les notes", "view_error": "Voir l'erreur", "retry": "<PERSON><PERSON><PERSON><PERSON>", "filter_status": "Filtrer par statut", "filter_version": "Filtrer par version", "filter_version_placeholder": "Rechercher une version...", "filter_date_from": "Date de début", "filter_date_to": "Date de fin", "clear_filters": "Effacer les filtres", "showing_results": "Affichage de {from} à {to} sur {total} résultats", "changelog_title": "Notes de version {version}", "no_changelog": "Aucune note de version disponible", "error_details": "<PERSON>é<PERSON> de l'erreur", "update_context": "Contexte de la mise à jour", "started_at": "<PERSON><PERSON><PERSON><PERSON>", "failed_at": "<PERSON><PERSON><PERSON>", "check_success": "Vérification réussie", "updates_found": "{count} mise(s) à jour trouvée(s)", "no_updates": "Aucune mise à jour disponible", "check_error": "Erreur de vérification", "downloading_version": "Téléchargement de la version {version}", "download_success": "Téléchargement réussi", "download_completed": "Téléchargement de la version {version} terminé", "download_error": "<PERSON><PERSON>ur de téléchargement", "cleanup_success": "<PERSON><PERSON><PERSON><PERSON>", "cleanup_completed": "{files} fichier(s) et {records} enregistrement(s) supprimés", "cleanup_error": "E<PERSON>ur de nettoyage", "settings_title": "Paramètres des mises à jour", "automatic_checking": "Vérification automatique", "enable_auto_check": "Activer la vérification automatique", "auto_check_description": "Vérifier automatiquement les nouvelles versions selon l'intervalle défini", "check_interval": "Intervalle de vérification", "check_interval_description": "Fréquence de vérification des mises à jour", "interval_1h": "Toutes les heures", "interval_6h": "Toutes les 6 heures", "interval_12h": "Toutes les 12 heures", "interval_24h": "Toutes les 24 heures", "interval_48h": "Toutes les 48 heures", "interval_7d": "Toutes les semaines", "automatic_download": "Téléchargement automatique", "enable_auto_download": "Activer le téléchargement automatique", "auto_download_description": "Télécharger automatiquement les mises à jour disponibles", "requires_auto_check": "Nécessite la vérification automatique", "automatic_installation": "Installation automatique", "enable_auto_install": "Activer l'installation automatique", "auto_install_description": "Installer automatiquement les mises à jour téléchargées", "requires_auto_download": "Nécessite le téléchargement automatique", "backup_settings": "Paramètres de sauvegarde", "backup_before_update": "Sauvegarder avant mise à jour", "backup_description": "<PERSON>réer automatiquement une sauvegarde avant chaque mise à jour", "notification_settings": "Paramètres de notification", "notification_email": "Email de notification", "notification_email_placeholder": "<EMAIL>", "notification_email_description": "Adresse email pour recevoir les notifications de mise à jour", "configuration_preview": "Aperçu de la configuration", "check_frequency": "Fréquence de vérification", "backup_enabled": "Sauvegarde activée", "reset_defaults": "Réinitialiser", "settings_saved": "Paramètres sauvegardés", "settings_updated": "Paramètres mis à jour avec succès", "settings_error": "<PERSON><PERSON><PERSON> <PERSON> sauve<PERSON>", "invalid_email": "Format d'email invalide", "interval_too_short": "L'intervalle doit être d'au moins 1 heure", "auto_install_requires_download": "L'installation automatique nécessite le téléchargement automatique", "auto_download_requires_check": "Le téléchargement automatique nécessite la vérification automatique", "minutes": "{count} minute(s)", "hours": "{count} heure(s)", "days": "{count} jour(s)", "preparing_download": "Préparation du téléchargement...", "preparing_install": "Préparation de l'installation...", "preparing": "Préparation...", "downloading": "Téléchargement en cours...", "installing": "Installation en cours...", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "processing": "Traitement en cours...", "step_validate_token": "Validation du token", "step_validate_token_desc": "Vérification de l'autorisation de téléchargement", "step_download_file": "Téléchargement du fichier", "step_download_file_desc": "Téléchargement de la mise à jour depuis le serveur", "step_verify_integrity": "Vérification de l'intégrité", "step_verify_integrity_desc": "Contrôle de l'intégrité du fichier téléchargé", "step_complete": "Finalisation", "step_complete_desc": "Finalisation du téléchargement", "step_backup": "<PERSON><PERSON><PERSON><PERSON>", "step_backup_desc": "Création d'une sauvegarde du système actuel", "step_extract": "Extraction", "step_extract_desc": "Extraction des fichiers de mise à jour", "step_apply": "Application", "step_apply_desc": "Application de la mise à jour au système", "step_cleanup": "Nettoyage", "step_cleanup_desc": "Nettoyage des fichiers temporaires", "version_details": "<PERSON><PERSON><PERSON> de la version", "update_id": "ID de mise à jour", "unknown_error": "<PERSON><PERSON><PERSON> inconnue", "install": "Installer", "installing_version": "Installation de la version {version}", "install_success": "Installation réussie", "install_completed": "Installation de la version {version} terminée", "install_error": "Erreur d'installation", "no_downloaded_version": "Aucune version téléchargée trouvée", "requirements_not_met": "Prérequis non satisfaits", "check_requirements_first": "Veuillez vérifier les prérequis d'installation", "requirements_check_error": "Erreur de vérification des prérequis", "rollback_success": "<PERSON><PERSON>", "rollback_completed": "Rollback vers la version {version} terminé", "rollback_error": "<PERSON><PERSON><PERSON> de rollback", "no_backup_available": "Aucune sauvegarde disponible", "confirm_rollback": "Êtes-vous sûr de vouloir revenir à la version {version} ?", "backup_id": "ID de sauvegarde", "update_to_version": "Mettre à jour vers cette version"}}
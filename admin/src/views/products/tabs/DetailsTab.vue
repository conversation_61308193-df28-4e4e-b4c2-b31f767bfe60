<template>
  <div class="details-tab">
    <div class="form-container">
      <!-- Informations de base -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-info-circle"></i>
          {{ t('products_services.sections.basic_info') }}
        </h3>
        
        <div class="form-grid">
          <!-- Nom du produit -->
          <div class="form-group">
            <label for="product-name" class="form-label required">
              {{ t('products_services.fields.name') }}
            </label>
            <input
              id="product-name"
              type="text"
              class="form-input"
              :class="{ 'error': errors.name }"
              :value="product.name"
              @input="updateField('name', ($event.target as HTMLInputElement)?.value)"
              :placeholder="t('products_services.placeholders.name')"
            />
            <div v-if="errors.name" class="error-message">
              {{ errors.name }}
            </div>
          </div>

          <!-- Slug -->
          <div class="form-group">
            <label for="product-slug" class="form-label">
              {{ t('products_services.fields.slug') }}
            </label>
            <input
              id="product-slug"
              type="text"
              class="form-input"
              :class="{ 'error': errors.slug }"
              :value="product.slug"
              @input="updateField('slug', ($event.target as HTMLInputElement)?.value)"
              :placeholder="t('products_services.placeholders.slug')"
            />
            <div v-if="errors.slug" class="error-message">
              {{ errors.slug }}
            </div>
            <div class="field-help">
              {{ t('products_services.help.slug') }}
            </div>
          </div>

          <!-- Catégorie -->
          <div class="form-group">
            <label for="product-category" class="form-label required">
              {{ t('products_services.fields.category') }}
            </label>
            <select
              id="product-category"
              class="form-select"
              :class="{ 'error': errors.group_id }"
              :value="product.group_id || ''"
              @change="updateField('group_id', ($event.target as HTMLSelectElement)?.value ? parseInt(($event.target as HTMLSelectElement).value) : null)"
            >
              <option value="">{{ t('products_services.placeholders.select_category') }}</option>
              <option
                v-for="category in categories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
            <div v-if="errors.group_id" class="error-message">
              {{ errors.group_id }}
            </div>
          </div>

          <!-- Type de produit -->
          <div class="form-group">
            <label for="product-type" class="form-label">
              {{ t('products_services.fields.type') }}
            </label>
            <select
              id="product-type"
              class="form-select"
              :value="product.product_type"
              @change="updateField('product_type', $event.target.value)"
            >
              <option value="shared_hosting">{{ t('products_services.types.shared_hosting') }}</option>
              <option value="reseller_hosting">{{ t('products_services.types.reseller_hosting') }}</option>
              <option value="vps">{{ t('products_services.types.vps') }}</option>
              <option value="dedicated_server">{{ t('products_services.types.dedicated_server') }}</option>
              <option value="domain">{{ t('products_services.types.domain') }}</option>
              <option value="ssl">{{ t('products_services.types.ssl') }}</option>
              <option value="other">{{ t('products_services.types.other') }}</option>
            </select>
          </div>

          <!-- Couleur -->
          <div class="form-group">
            <label for="product-color" class="form-label">
              {{ t('products_services.fields.color') }}
            </label>
            <div class="color-input-group">
              <input
                id="product-color"
                type="color"
                class="form-color"
                :value="product.color"
                @input="updateField('color', $event.target.value)"
              />
              <input
                type="text"
                class="form-input color-text"
                :value="product.color"
                @input="updateField('color', $event.target.value)"
                placeholder="#0066ff"
              />
            </div>
          </div>

          <!-- Statut -->
          <div class="form-group">
            <label for="product-status" class="form-label">
              {{ t('products_services.fields.status') }}
            </label>
            <select
              id="product-status"
              class="form-select"
              :value="product.status"
              @change="updateField('status', $event.target.value)"
            >
              <option value="active">{{ t('products_services.status.active') }}</option>
              <option value="inactive">{{ t('products_services.status.inactive') }}</option>
              <option value="maintenance">{{ t('products_services.status.maintenance') }}</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Descriptions -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-align-left"></i>
          {{ t('products_services.sections.descriptions') }}
        </h3>
        
        <div class="form-grid">
          <!-- Description courte -->
          <div class="form-group full-width">
            <label for="product-short-description" class="form-label">
              {{ t('products_services.fields.short_description') }}
            </label>
            <textarea
              id="product-short-description"
              class="form-textarea"
              rows="3"
              :value="product.short_description"
              @input="updateField('short_description', $event.target.value)"
              :placeholder="t('products_services.placeholders.short_description')"
            ></textarea>
            <div class="field-help">
              {{ t('products_services.help.short_description') }}
            </div>
          </div>

          <!-- Description complète -->
          <div class="form-group full-width">
            <label for="product-description" class="form-label">
              {{ t('products_services.fields.description') }}
            </label>
            <textarea
              id="product-description"
              class="form-textarea"
              rows="6"
              :value="product.description"
              @input="updateField('description', $event.target.value)"
              :placeholder="t('products_services.placeholders.description')"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Options avancées -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-cogs"></i>
          {{ t('products_services.sections.advanced_options') }}
        </h3>
        
        <div class="form-grid">
          <!-- Email de bienvenue -->
          <div class="form-group">
            <label for="welcome-email" class="form-label">
              {{ t('products_services.fields.welcome_email') }}
            </label>
            <select
              id="welcome-email"
              class="form-select"
              :value="product.welcome_email_id || ''"
              @change="updateField('welcome_email_id', $event.target.value ? parseInt($event.target.value) : null)"
            >
              <option value="">{{ t('products_services.placeholders.no_welcome_email') }}</option>
              <option
                v-for="email in welcomeEmails"
                :key="email.id"
                :value="email.id"
              >
                {{ email.name }}
              </option>
            </select>
          </div>

          <!-- Options booléennes -->
          <div class="form-group">
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  class="form-checkbox"
                  :checked="product.featured"
                  @change="updateField('featured', $event.target.checked)"
                />
                <span class="checkbox-text">{{ t('products_services.fields.featured') }}</span>
              </label>
              
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  class="form-checkbox"
                  :checked="product.hidden"
                  @change="updateField('hidden', $event.target.checked)"
                />
                <span class="checkbox-text">{{ t('products_services.fields.hidden') }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useProductGroupsStore } from '@/stores/product-groups'
import logger from '@/services/logger'

// Props
interface Props {
  product: any
  errors: Record<string, string>
  mode: 'create' | 'edit'
}

const props = defineProps<Props>()

// Émissions
const emit = defineEmits<{
  update: [field: string, value: any]
}>()

// Composables
const { t } = useI18n()
const productGroupsStore = useProductGroupsStore()

// État local
const categories = ref<any[]>([])
const welcomeEmails = ref<any[]>([
  { id: 1, name: 'Email de bienvenue par défaut' },
  { id: 2, name: 'Email de bienvenue hosting' },
  { id: 3, name: 'Email de bienvenue services' }
])

// Méthodes
const updateField = (field: string, value: any) => {
  logger.debug('Mise à jour du champ depuis DetailsTab', { field, value })
  emit('update', field, value)
}

// Helper pour les événements d'input
const handleInputChange = (field: string, event: Event) => {
  const target = event.target as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
  updateField(field, target?.value)
}

// Helper pour les événements de checkbox
const handleCheckboxChange = (field: string, event: Event) => {
  const target = event.target as HTMLInputElement
  updateField(field, target?.checked)
}

// Helper pour les selects avec conversion en nombre
const handleSelectNumberChange = (field: string, event: Event) => {
  const target = event.target as HTMLSelectElement
  const value = target?.value
  updateField(field, value ? parseInt(value) : null)
}

const loadCategories = async () => {
  try {
    await productGroupsStore.fetchProductGroups()
    categories.value = productGroupsStore.productGroups
    logger.info('Catégories chargées', { count: categories.value.length })
  } catch (error) {
    logger.error('Erreur lors du chargement des catégories', { error })
    // Catégories par défaut en cas d'erreur
    categories.value = [
      { id: 1, name: 'Hébergement Web' },
      { id: 2, name: 'Serveurs VPS' },
      { id: 3, name: 'Domaines' }
    ]
  }
}

// Lifecycle
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
@import '@/assets/css/pages/products.css';
@import '@/assets/css/components/common-layout.css';
@import '@/assets/css/components/forms.css';
@import '@/assets/css/components/buttons.css';

/* Utilisation des classes CSS existantes uniquement */
.form-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
}

.section-title i {
  color: var(--primary-blue);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label.required::after {
  content: ' *';
  color: var(--error);
}

.color-input-group {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.form-color {
  width: 60px;
  height: 40px;
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  cursor: pointer;
  background: var(--glass-bg);
}

.color-text {
  flex: 1;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  color: var(--text-color);
}

.form-checkbox {
  width: 18px;
  height: 18px;
}

.error-message {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.field-help {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}
</style>

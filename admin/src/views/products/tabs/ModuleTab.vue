<template>
  <div class="module-tab">
    <div class="form-container">
      <!-- Section Configuration du module -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-cog"></i>
          {{ t('products_services.module.description') }}
        </h3>
        
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label required">{{ t('products_services.module.select_module') }}</label>
            <select
              class="form-select"
              :class="{ error: errors.server_type }"
              :value="product.server_type"
              @change="handleModuleChange($event.target.value)"
            >
              <option value="">{{ t('products_services.module.select_module_placeholder') }}</option>
              <option
                v-for="module in availableModules"
                :key="module.name"
                :value="module.name"
              >
                {{ module.display_name || module.name }}
              </option>
            </select>
            <div v-if="errors.server_type" class="error-message">{{ errors.server_type }}</div>
            <div v-if="selectedModuleInfo" class="field-help">
              {{ selectedModuleInfo.description }}
            </div>
          </div>

          <div v-if="product.server_type" class="form-group">
            <label class="form-label">{{ t('products_services.module.select_server') }}</label>
            <select
              class="form-select"
              :value="product.server_id || ''"
              @change="handleServerChange($event.target.value)"
            >
              <option value="">{{ t('products_services.module.select_server_placeholder') }}</option>
              <option
                v-for="server in availableServers"
                :key="server.id"
                :value="server.id"
              >
                {{ server.name }} ({{ server.hostname }})
              </option>
            </select>
            <div class="field-help">{{ t('products_services.module.server_help') }}</div>
          </div>
          
          <div class="form-group">
            <label class="form-label">{{ t('products_services.module.provisioning_type') }}</label>
            <div class="radio-group">
              <label class="radio-label">
                <input
                  type="radio"
                  name="provisioning_type"
                  value="manual"
                  :checked="isManualProvisioning"
                  @change="handleProvisioningTypeChange('manual')"
                />
                <span class="radio-text">{{ t('products_services.module.manual_provision') }}</span>
              </label>
              <label class="radio-label">
                <input
                  type="radio"
                  name="provisioning_type"
                  value="automatic"
                  :checked="isAutomaticProvisioning"
                  @change="handleProvisioningTypeChange('automatic')"
                />
                <span class="radio-text">{{ t('products_services.module.auto_provision') }}</span>
              </label>
            </div>
            <div class="field-help">
              {{ isAutomaticProvisioning ?
                t('products_services.module.auto_provision_description') :
                t('products_services.module.manual_provision_description')
              }}
            </div>
          </div>
        </div>
      </div>

      <!-- Section Configuration automatique -->
      <div v-if="isAutomaticProvisioning" class="form-section">
        <h3 class="section-title">
          <i class="fas fa-magic"></i>
          {{ t('products_services.module.automation') }}
        </h3>

        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">{{ t('products_services.module.auto_setup_trigger') }}</label>
            <select
              class="form-select"
              :value="autoSetupTrigger"
              @change="updateAutoSetupTrigger($event.target.value)"
            >
              <option value="order">{{ t('products_services.module.auto_setup_on_order') }}</option>
              <option value="payment">{{ t('products_services.module.auto_setup_on_payment') }}</option>
              <option value="pending">{{ t('products_services.module.auto_setup_on_pending') }}</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Section Configuration spécifique au module -->
      <div v-if="product.server_type && moduleConfigFields.length > 0" class="form-section">
        <h3 class="section-title">
          <i class="fas fa-sliders-h"></i>
          {{ t('products_services.module.module_configuration') }}
        </h3>
        
        <div class="form-grid">
          <div 
            v-for="field in moduleConfigFields" 
            :key="field.name" 
            class="form-group"
            :class="{ 'full-width': field.type === 'textarea' }"
          >
            <label class="form-label" :class="{ required: field.required }">
              {{ field.label }}
            </label>
            
            <!-- Champ texte -->
            <input
              v-if="field.type === 'text' || field.type === 'number'"
              :type="field.type"
              class="form-input"
              :value="getModuleFieldValue(field.name)"
              @input="updateModuleField(field.name, $event.target.value)"
              :placeholder="field.placeholder"
              :min="field.min"
              :max="field.max"
            />
            
            <!-- Champ select -->
            <select
              v-else-if="field.type === 'select'"
              class="form-select"
              :value="getModuleFieldValue(field.name)"
              @change="updateModuleField(field.name, $event.target.value)"
            >
              <option value="">{{ field.placeholder || 'Sélectionner...' }}</option>
              <option 
                v-for="option in field.options" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
            
            <!-- Champ textarea -->
            <textarea
              v-else-if="field.type === 'textarea'"
              class="form-textarea"
              :value="getModuleFieldValue(field.name)"
              @input="updateModuleField(field.name, $event.target.value)"
              :placeholder="field.placeholder"
              rows="4"
            ></textarea>
            
            <!-- Champ checkbox -->
            <label v-else-if="field.type === 'checkbox'" class="checkbox-label">
              <input
                type="checkbox"
                class="form-checkbox"
                :checked="getModuleFieldValue(field.name)"
                @change="updateModuleField(field.name, $event.target.checked)"
              />
              <span class="checkbox-text">{{ field.label }}</span>
            </label>
            
            <div v-if="field.description" class="field-help">
              {{ field.description }}
              <span v-if="field.unit"> ({{ field.unit }})</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Section Package/Plan -->
      <div v-if="product.server_type && moduleTemplates.length > 0" class="form-section">
        <h3 class="section-title">
          <i class="fas fa-box"></i>
          {{ t('products_services.module.select_plan') }}
        </h3>
        
        <div class="form-grid">
          <div class="form-group full-width">
            <label class="form-label">{{ t('products_services.module.package_name') }}</label>
            <select 
              class="form-select"
              :value="product.package_name"
              @change="updateField('package_name', $event.target.value)"
            >
              <option value="">{{ t('products_services.module.select_plan_option') }}</option>
              <option 
                v-for="template in moduleTemplates" 
                :key="template.id" 
                :value="template.id"
              >
                {{ template.name }}
                <span v-if="template.price"> - {{ template.price }}€</span>
              </option>
            </select>
            <div class="field-help">{{ t('products_services.module.package_help') }}</div>
          </div>
        </div>
      </div>

      <!-- Message si aucune configuration -->
      <div v-if="product.server_type && !hasModuleConfig" class="form-section">
        <div class="no-config-message">
          <i class="fas fa-info-circle"></i>
          <p>{{ t('products_services.module.no_config_available') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { useProductEditStore } from '@/stores/product-edit'
import { useModulesStore } from '@/stores/modules'
import { ApiService } from '@/services/api'
import logger from '@/services/logger'

// Composables
const { t } = useI18n()
const productEditStore = useProductEditStore()
const modulesStore = useModulesStore()

// Store state
const { product, errors } = storeToRefs(productEditStore)
const { updateField } = productEditStore

const {
  modules,
  selectedModule,
  moduleTemplates,
  productConfigFields: moduleConfigFields,
  loading: modulesLoading
} = storeToRefs(modulesStore)

// État local pour les serveurs
const availableServers = ref<any[]>([])
const loadingServers = ref(false)

// Computed
const availableModules = computed(() => {
  return modules.value.servers || []
})

const selectedModuleInfo = computed(() => {
  if (!product.value?.server_type) return null
  return availableModules.value.find(m => m.name === product.value?.server_type) || null
})

const hasModuleConfig = computed(() => {
  return moduleConfigFields.value.length > 0 || moduleTemplates.value.length > 0
})

// Computed pour la gestion du provisionnement
const isManualProvisioning = computed(() => {
  return product.value?.provisioning_type === 'manual'
})

const isAutomaticProvisioning = computed(() => {
  const type = product.value?.provisioning_type
  return type && ['auto_setup_on_order', 'auto_setup_on_payment', 'auto_setup_on_pending'].includes(type)
})

const autoSetupTrigger = computed(() => {
  const type = product.value?.provisioning_type
  if (type === 'auto_setup_on_order') return 'order'
  if (type === 'auto_setup_on_payment') return 'payment'
  if (type === 'auto_setup_on_pending') return 'pending'
  return 'payment' // Valeur par défaut
})

// Méthodes
const handleModuleChange = async (moduleType: string) => {
  logger.info('[ModuleTab] Changement de module', { moduleType })
  updateField('server_type', moduleType)
  updateField('server_id', '') // Réinitialiser le serveur sélectionné

  if (moduleType) {
    logger.info('[ModuleTab] Chargement des serveurs pour le module', { moduleType })
    // Charger les serveurs compatibles avec ce module
    await loadServers(moduleType)
    // Charger la configuration du module (sans les templates)
    await modulesStore.loadModuleConfig(moduleType)
    // NE PAS charger les templates ici - attendre la sélection du serveur
  } else {
    // Réinitialiser les champs liés
    updateField('package_name', '')
    availableServers.value = []
    modulesStore.clearModuleConfig()
  }
}

const handleServerChange = async (serverId: string) => {
  updateField('server_id', serverId)

  if (serverId && product.value?.server_type) {
    // Charger les templates/plans pour ce serveur et module
    await modulesStore.loadModuleTemplates(product.value.server_type, serverId)
  } else {
    // Réinitialiser les templates
    modulesStore.clearModuleConfig()
  }
}

const loadServers = async (moduleType: string) => {
  try {
    logger.info('[ModuleTab] Début chargement serveurs', { moduleType })
    loadingServers.value = true
    const response = await ApiService.routes.admin.system.server.list()

    logger.debug('[ModuleTab] Réponse API serveurs', { response: response.data })

    // L'API retourne { "servers": [...] } au lieu de { "success": true, "data": [...] }
    const serversData = response.data.servers || response.data.data || []

    if (serversData && serversData.length > 0) {
      logger.debug('[ModuleTab] Tous les serveurs récupérés', { count: serversData.length })

      // Filtrer les serveurs compatibles avec le module sélectionné
      // Comparaison insensible à la casse pour le type
      availableServers.value = serversData.filter((server: any) =>
        server.type && server.type.toLowerCase() === moduleType.toLowerCase() &&
        (server.status === 'active' || server.active === true)
      )

      logger.info('[ModuleTab] Serveurs filtrés pour le module', {
        moduleType,
        serversFound: availableServers.value.length,
        servers: availableServers.value.map(s => ({ id: s.id, name: s.name, hostname: s.hostname, type: s.type }))
      })
    } else {
      logger.warn('[ModuleTab] Aucun serveur trouvé dans la réponse', {
        responseStructure: Object.keys(response.data),
        serversData: serversData
      })
      availableServers.value = []
    }
  } catch (error) {
    logger.error('[ModuleTab] Erreur lors du chargement des serveurs', { moduleType, error })
    availableServers.value = []
  } finally {
    loadingServers.value = false
  }
}

const handleProvisioningTypeChange = (mode: string) => {
  if (mode === 'manual') {
    updateField('provisioning_type', 'manual')
    updateField('auto_provision', false)
  } else {
    // Mode automatique - définir le trigger par défaut
    updateField('provisioning_type', 'auto_setup_on_payment')
    updateField('auto_provision', true)
  }
}

const updateAutoSetupTrigger = (trigger: string) => {
  // Convertir le trigger en valeur provisioning_type
  const provisioningTypeMap: Record<string, string> = {
    'order': 'auto_setup_on_order',
    'payment': 'auto_setup_on_payment',
    'pending': 'auto_setup_on_pending'
  }

  updateField('provisioning_type', provisioningTypeMap[trigger] || 'auto_setup_on_payment')
  updateField('auto_provision', true)
}

const getModuleFieldValue = (fieldName: string): any => {
  if (!product.value?.options) return ''
  return product.value.options[fieldName] || ''
}

const updateModuleField = (fieldName: string, value: any) => {
  if (!product.value) return
  
  const currentOptions = product.value.options || {}
  const updatedOptions = {
    ...currentOptions,
    [fieldName]: value
  }
  
  updateField('options', updatedOptions)
}

// Lifecycle
onMounted(async () => {
  // Charger les modules disponibles
  await modulesStore.loadModules('servers')

  // Si un module est déjà sélectionné, charger sa configuration ET les serveurs
  if (product.value?.server_type) {
    await modulesStore.loadModuleConfig(product.value.server_type)
    await loadServers(product.value.server_type)

    // Si un serveur est aussi sélectionné, charger les templates
    if (product.value?.server_id) {
      await modulesStore.loadModuleTemplates(product.value.server_type, product.value.server_id)
    }
  }
})

// Watchers
watch(() => product.value?.server_type, async (newModuleType) => {
  if (newModuleType) {
    await modulesStore.loadModuleConfig(newModuleType)
    await loadServers(newModuleType)
    // NE PAS charger les templates ici - attendre la sélection du serveur
  }
})
</script>

<style scoped>
@import '@/assets/css/pages/products.css';
@import '@/assets/css/components/common-layout.css';
@import '@/assets/css/components/forms.css';
@import '@/assets/css/components/buttons.css';

/* Utilisation des classes CSS existantes */
.form-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
}

.section-title i {
  color: var(--primary-blue);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label.required::after {
  content: ' *';
  color: var(--error);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  color: var(--text-color);
}

.radio-text {
  font-weight: 500;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  color: var(--text-color);
}

.error-message {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.field-help {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.no-config-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  color: var(--text-muted);
}

.no-config-message i {
  color: var(--primary-blue);
  font-size: 1.25rem;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}
</style>

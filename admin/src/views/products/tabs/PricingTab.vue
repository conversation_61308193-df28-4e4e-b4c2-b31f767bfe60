<template>
  <div class="pricing-tab">
    <div class="form-container">
      <!-- Section Configuration de base -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="fas fa-cog"></i>
          {{ t('products_services.pricing.billing_mode') }}
        </h3>
        
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">{{ t('products_services.pricing.billing_mode') }}</label>
            <div class="radio-group">
              <label class="radio-label">
                <input
                  type="radio"
                  name="billing_mode"
                  value="single"
                  :checked="!product.multiple_cycles_enabled"
                  @change="handleBillingModeChange(false)"
                />
                <span class="radio-text">{{ t('products_services.pricing.single_cycle') }}</span>
              </label>
              <label class="radio-label">
                <input
                  type="radio"
                  name="billing_mode"
                  value="multiple"
                  :checked="product.multiple_cycles_enabled"
                  @change="handleBillingModeChange(true)"
                />
                <span class="radio-text">{{ t('products_services.pricing.multiple_cycles') }}</span>
              </label>
            </div>
            <div class="field-help">
              {{ product.multiple_cycles_enabled ? 
                t('products_services.pricing.multiple_cycles_hint') : 
                t('products_services.pricing.single_cycle_hint') 
              }}
            </div>
          </div>
          

        </div>
      </div>

      <!-- Section Cycle unique -->
      <div v-if="!product.multiple_cycles_enabled" class="form-section">
        <h3 class="section-title">
          <i class="fas fa-dollar-sign"></i>
          {{ t('products_services.pricing.base_price') }}
        </h3>
        
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label required">{{ t('products_services.pricing.billing_cycle') }}</label>
            <select 
              class="form-select"
              :value="product.billing_cycle"
              @change="updateField('billing_cycle', $event.target.value)"
            >
              <option value="monthly">{{ t('products_services.pricing.monthly') }}</option>
              <option value="quarterly">{{ t('products_services.pricing.quarterly') }}</option>
              <option value="semi_annually">{{ t('products_services.pricing.semiannually') }}</option>
              <option value="annually">{{ t('products_services.pricing.annually') }}</option>
              <option value="biennially">{{ t('products_services.pricing.biennially') }}</option>
              <option value="triennially">{{ t('products_services.pricing.triennially') }}</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label required">{{ t('products_services.pricing.price') }}</label>
            <div class="price-input-group">
              <input
                type="number"
                step="0.01"
                min="0"
                class="form-input"
                :class="{ error: errors.price }"
                :value="product.price"
                @input="updateField('price', parseFloat($event.target.value) || 0)"
                placeholder="0.00"
              />
              <span class="currency-symbol">€</span>
            </div>
            <div v-if="errors.price" class="error-message">{{ errors.price }}</div>
          </div>
          
          <div class="form-group">
            <label class="form-label">{{ t('products_services.pricing.setup_fee') }}</label>
            <div class="price-input-group">
              <input
                type="number"
                step="0.01"
                min="0"
                class="form-input"
                :value="product.setup_fee"
                @input="updateField('setup_fee', parseFloat($event.target.value) || 0)"
                placeholder="0.00"
              />
              <span class="currency-symbol">€</span>
            </div>
            <div class="field-help">{{ t('products_services.pricing.setup_fee_hint') }}</div>
          </div>
        </div>
      </div>

      <!-- Section Cycles multiples -->
      <div v-else class="form-section">
        <h3 class="section-title">
          <i class="fas fa-calendar-alt"></i>
          {{ t('products_services.pricing.multiple_cycles') }}
        </h3>

        <div class="cycles-container">
          <div v-for="cycle in availableCycles" :key="cycle.key" class="cycle-card">
            <div class="cycle-header">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  class="form-checkbox"
                  :checked="isCycleEnabled(cycle.key)"
                  @change="toggleCycle(cycle.key, $event.target.checked)"
                />
                <span class="cycle-name">{{ cycle.label }}</span>
              </label>

              <button
                v-if="isCycleEnabled(cycle.key)"
                type="button"
                class="btn btn-sm"
                :class="{ 'btn-primary': isDefaultCycle(cycle.key), 'btn-secondary': !isDefaultCycle(cycle.key) }"
                @click="setDefaultCycle(cycle.key)"
              >
                {{ isDefaultCycle(cycle.key) ? t('products_services.pricing.default') : t('products_services.pricing.set_default') }}
              </button>
            </div>

            <div v-if="isCycleEnabled(cycle.key)" class="cycle-pricing">
              <div class="price-fields">
                <div class="form-group">
                  <label class="form-label">{{ t('products_services.pricing.price') }}</label>
                  <div class="price-input-group">
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      class="form-input"
                      :value="getCyclePrice(cycle.key)"
                      @input="updateCyclePrice(cycle.key, parseFloat($event.target.value) || 0)"
                      placeholder="0.00"
                    />
                    <span class="currency-symbol">€</span>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">{{ t('products_services.pricing.setup_fee') }}</label>
                  <div class="price-input-group">
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      class="form-input"
                      :value="getCycleSetupFee(cycle.key)"
                      @input="updateCycleSetupFee(cycle.key, parseFloat($event.target.value) || 0)"
                      placeholder="0.00"
                    />
                    <span class="currency-symbol">€</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="enabledCycles.length === 0" class="validation-message error">
          {{ t('products_services.pricing.at_least_one_cycle') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { useProductEditStore } from '@/stores/product-edit'

// Composables
const { t } = useI18n()
const productEditStore = useProductEditStore()

// Store state
const { product, errors } = storeToRefs(productEditStore)
const { updateField } = productEditStore

// Cycles disponibles
const availableCycles = computed(() => [
  { key: 'monthly', label: t('products_services.pricing.monthly') },
  { key: 'quarterly', label: t('products_services.pricing.quarterly') },
  { key: 'semi_annually', label: t('products_services.pricing.semiannually') },
  { key: 'annually', label: t('products_services.pricing.annually') },
  { key: 'biennially', label: t('products_services.pricing.biennially') },
  { key: 'triennially', label: t('products_services.pricing.triennially') }
])

// Cycles activés (pour mode multiple)
const enabledCycles = computed(() => {
  if (!product.value?.available_cycles) return []
  return product.value.available_cycles.split(',')
    .filter(cycle => cycle.trim())
    .map(cycle => cycle.trim() === 'semiannually' ? 'semi_annually' : cycle.trim())
})

// Méthodes pour la gestion des cycles
const isCycleEnabled = (cycleKey: string): boolean => {
  // Conversion pour vérifier : semiannually → semi_annually
  const dbCycleKey = cycleKey === 'semiannually' ? 'semi_annually' : cycleKey
  return enabledCycles.value.includes(dbCycleKey)
}

const isDefaultCycle = (cycleKey: string): boolean => {
  return product.value?.default_cycle === cycleKey
}

const toggleCycle = (cycleKey: string, enabled: boolean) => {
  if (!product.value) return

  let cycles = enabledCycles.value

  if (enabled) {
    if (!cycles.includes(cycleKey)) {
      cycles.push(cycleKey)
    }
  } else {
    cycles = cycles.filter(c => c !== cycleKey)
    // Si on désactive le cycle par défaut, choisir un autre
    if (product.value.default_cycle === cycleKey && cycles.length > 0) {
      updateField('default_cycle', cycles[0])
    }
  }

  // Conversion pour la base de données : semi_annually reste, mais on s'assure de la cohérence
  const dbCycles = cycles.map(cycle => cycle === 'semiannually' ? 'semi_annually' : cycle)
  updateField('available_cycles', dbCycles.join(','))
}

const setDefaultCycle = (cycleKey: string) => {
  updateField('default_cycle', cycleKey)
}

// Conversion clé interface → colonne base de données
const getColumnKey = (cycleKey: string): string => {
  return cycleKey === 'semi_annually' ? 'semiannually' : cycleKey
}

const getCyclePrice = (cycleKey: string): number => {
  if (!product.value) return 0
  const columnKey = getColumnKey(cycleKey)
  return (product.value as any)[`price_${columnKey}`] || 0
}

const updateCyclePrice = (cycleKey: string, price: number) => {
  const columnKey = getColumnKey(cycleKey)
  updateField(`price_${columnKey}` as any, price)
}

const getCycleSetupFee = (cycleKey: string): number => {
  if (!product.value) return 0
  const columnKey = getColumnKey(cycleKey)
  return (product.value as any)[`setup_fee_${columnKey}`] || 0
}

const updateCycleSetupFee = (cycleKey: string, fee: number) => {
  const columnKey = getColumnKey(cycleKey)
  updateField(`setup_fee_${columnKey}` as any, fee)
}

// Gestion du changement de mode de facturation
const handleBillingModeChange = (isMultiple: boolean) => {
  updateField('multiple_cycles_enabled', isMultiple)
  // Logique métier : cycle unique = ponctuel, cycles multiples = récurrent
  updateField('recurring', isMultiple)
}
</script>

<style scoped>
@import '@/assets/css/pages/products.css';
@import '@/assets/css/components/common-layout.css';
@import '@/assets/css/components/forms.css';
@import '@/assets/css/components/buttons.css';

/* Utilisation des classes CSS existantes */
.form-section {
  margin-bottom: var(--spacing-xxl);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
}

.section-title i {
  color: var(--primary-blue);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label.required::after {
  content: ' *';
  color: var(--error);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  color: var(--text-color);
}

.radio-text {
  font-weight: 500;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  color: var(--text-color);
}

.price-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-symbol {
  position: absolute;
  right: var(--spacing-md);
  color: var(--text-muted);
  font-weight: 500;
  pointer-events: none;
}

.price-input-group .form-input {
  padding-right: 2.5rem;
}

.error-message {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

.field-help {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
}

/* Styles pour les cycles multiples */
.cycles-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.cycle-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: var(--transition-fast);
}

.cycle-card:hover {
  border-color: var(--primary-blue);
  box-shadow: 0 4px 20px rgb(0 102 255 / 10%);
}

.cycle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--glass-border);
}

.cycle-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.cycle-pricing {
  margin-top: var(--spacing-md);
}

.price-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.validation-message {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  text-align: center;
  font-weight: 500;
  margin-top: var(--spacing-lg);
}

.validation-message.error {
  background: rgb(239 68 68 / 10%);
  border: 1px solid var(--error);
  color: var(--error);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.875rem;
}
</style>

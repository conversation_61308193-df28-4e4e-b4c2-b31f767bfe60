<template>
  <div class="edit-product-page">
    <!-- Header avec titre et actions -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            {{ isCreateMode ? 'Créer un nouveau produit' : 'Modifier le produit' }}
          </h1>
          <p class="page-subtitle" v-if="product?.name">
            {{ product.name }}
          </p>
        </div>
        
        <div class="header-actions">
          <button 
            type="button" 
            class="btn btn-secondary"
            @click="cancelEdit"
            :disabled="saving"
          >
            {{ t('common.cancel') }}
          </button>
          
          <button 
            type="button" 
            class="btn btn-primary"
            @click="saveProduct"
            :disabled="!isValid || saving"
          >
            <i class="fas fa-spinner fa-spin" v-if="saving"></i>
            <i class="fas fa-save" v-else></i>
            {{ saving ? t('common.saving') : t('common.save') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Indicateur de chargement -->
    <div v-if="loading" class="loading-container">
      <div class="spinner-loading"></div>
      <p>{{ t('common.loading') }}...</p>
    </div>

    <!-- Contenu principal -->
    <div v-else-if="product" class="content-container">
      <!-- Navigation par onglets -->
      <div class="tabs-navigation">
        <nav class="tabs-nav">
          <button
            v-for="tab in availableTabs"
            :key="tab.key"
            :class="['tab-button', { active: activeTab === tab.key }]"
            @click="activeTab = tab.key"
          >
            <i :class="tab.icon"></i>
            {{ t(tab.label) }}
          </button>
        </nav>
      </div>

      <!-- Contenu des onglets -->
      <div class="tab-content">
        <!-- Onglet Détails -->
        <DetailsTab
          v-if="activeTab === 'details'"
          :product="product"
          :errors="errors"
          :mode="mode"
          @update="updateField"
        />

        <!-- Onglet Tarification -->
        <PricingTab
          v-else-if="activeTab === 'pricing'"
        />

        <!-- Onglet Module -->
        <ModuleTab
          v-else-if="activeTab === 'module'"
        />

        <!-- Autres onglets (à implémenter) -->
        <div v-else class="tab-placeholder">
          <div class="placeholder-content">
            <i class="fas fa-tools fa-3x"></i>
            <h3>Onglet en construction</h3>
            <p>L'onglet "{{ getTabLabel(activeTab) }}" sera implémenté prochainement.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- État d'erreur -->
    <div v-else class="error-container">
      <div class="error-content">
        <i class="fas fa-exclamation-triangle fa-3x"></i>
        <h3>{{ t('common.error') }}</h3>
        <p>{{ errors.general || t('products_services.errors.load_failed') }}</p>
        <button class="btn btn-primary" @click="retryLoad">
          {{ t('common.retry') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useProductEditStore } from '@/stores/product-edit'
import { useNotificationStore } from '@/stores/notifications'
import logger from '@/services/logger'
import DetailsTab from './tabs/DetailsTab.vue'
import PricingTab from './tabs/PricingTab.vue'
import ModuleTab from './tabs/ModuleTab.vue'

// Composables
const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const productEditStore = useProductEditStore()
const notificationStore = useNotificationStore()

// État local
const activeTab = ref('details')

// Getters du store (utilisation de storeToRefs pour la réactivité)
const {
  product,
  mode,
  loading,
  saving,
  errors,
  isValid
} = storeToRefs(productEditStore)

const {
  isCreateMode
} = productEditStore

// Configuration des onglets
const availableTabs = computed(() => [
  { key: 'details', label: 'products_services.tabs.details', icon: 'fas fa-info-circle' },
  { key: 'pricing', label: 'products_services.tabs.pricing', icon: 'fas fa-dollar-sign' },
  { key: 'module', label: 'products_services.tabs.module', icon: 'fas fa-cog' },
  { key: 'custom-fields', label: 'products_services.tabs.custom_fields', icon: 'fas fa-list' },
  { key: 'cross-sells', label: 'products_services.tabs.cross_sells', icon: 'fas fa-shopping-cart' },
  { key: 'links', label: 'products_services.tabs.links', icon: 'fas fa-link' }
])

// Méthodes
const getTabLabel = (tabKey: string) => {
  const tab = availableTabs.value.find(t => t.key === tabKey)
  return tab ? t(tab.label) : tabKey
}

const updateField = (field: string, value: any) => {
  productEditStore.updateField(field as any, value)
}

const saveProduct = async () => {
  try {
    const result = await productEditStore.saveProduct()
    
    if (result.success) {
      notificationStore.addNotification({
        title: t('common.success'),
        message: isCreateMode
          ? t('products_services.messages.created_success')
          : t('products_services.messages.updated_success'),
        type: 'success'
      })
      
      // Rediriger vers la liste des produits après sauvegarde
      router.push('/products')
    } else {
      notificationStore.addNotification({
        title: t('common.error'),
        message: result.message || t('products_services.errors.save_failed'),
        type: 'error'
      })
    }
  } catch (error) {
    logger.error('Erreur lors de la sauvegarde', { error })
    notificationStore.addNotification({
      title: t('common.error'),
      message: t('products_services.errors.save_failed'),
      type: 'error'
    })
  }
}

const cancelEdit = () => {
  // TODO: Vérifier s'il y a des changements non sauvegardés
  router.push('/products')
}

const retryLoad = () => {
  initializeView()
}

const initializeView = async () => {
  try {
    const productId = route.params.id as string
    
    if (productId === 'create') {
      // Mode création
      productEditStore.initCreate()
      logger.info('Mode création initialisé')
    } else {
      // Mode édition
      const id = parseInt(productId, 10)
      if (isNaN(id)) {
        throw new Error('ID de produit invalide')
      }
      
      await productEditStore.initEdit(id)
      logger.info('Mode édition initialisé', { productId: id })
    }
  } catch (error) {
    logger.error('Erreur lors de l\'initialisation de la vue', { error })
    notificationStore.addNotification({
      title: t('common.error'),
      message: t('products_services.errors.load_failed'),
      type: 'error'
    })
  }
}

// Lifecycle
onMounted(() => {
  initializeView()
})

// Watcher pour détecter les changements de route
watch(() => route.params.id, () => {
  initializeView()
})

// Nettoyage lors de la destruction du composant
onUnmounted(() => {
  productEditStore.reset()
})
</script>

<style scoped>
@import '@/assets/css/pages/products.css';
@import '@/assets/css/components/common-layout.css';
@import '@/assets/css/components/forms.css';
@import '@/assets/css/components/buttons.css';

/* Utilisation des classes CSS existantes */
.edit-product-page {
  padding: var(--spacing-xl);
}

.page-header {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--glass-shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.page-subtitle {
  color: var(--text-muted);
  margin: 0.25rem 0 0 0;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

.content-container {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--glass-shadow);
}

.tabs-navigation {
  margin-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--glass-border);
}

.tabs-nav {
  display: flex;
  gap: var(--spacing-sm);
}

.tab-button {
  padding: var(--spacing-md) var(--spacing-lg);
  background: transparent;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition-fast);
  border-bottom: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.tab-button:hover {
  color: var(--text-color);
  background: var(--glass-bg);
}

.tab-button.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
  background: var(--glass-bg);
}

.tab-content {
  padding: var(--spacing-xl);
}

.tab-placeholder {
  text-align: center;
  padding: var(--spacing-xxl);
}

.placeholder-content i {
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.placeholder-content h3 {
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.placeholder-content p {
  color: var(--text-muted);
}

/* États de chargement et d'erreur utilisant les classes existantes */
.loading-container {
  @extend .loading-state;
}

.error-container {
  @extend .empty-state;
}

.error-content {
  max-width: 400px;
}

.error-content i {
  color: var(--error);
  margin-bottom: var(--spacing-md);
}
</style>

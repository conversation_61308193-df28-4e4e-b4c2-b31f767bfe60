<?php
/**
 * <PERSON>dèle pour les produits
 */

namespace TechCMS\Api\V1\Models;

use TechCMS\Common\Core\Database;
use TechCMS\Common\Core\Logger;

class Product {
    protected $db;
    protected $table = 'products';

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Récupère tous les produits
     */
    public function getAll() {
        $query = "SELECT * FROM {$this->table} WHERE status != 'deleted' ORDER BY name ASC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Récupère un produit par son ID
     */
    public function getById($id) {
        $query = "SELECT * FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Crée un nouveau produit
     */
    public function create($data) {
        // Debug des données reçues
        Logger::channel('api')->debug("[Product Model] Données reçues pour création", ['data' => $data]);
        
        $name = $data['name'];
        $slug = $data['slug'] ?? $this->generateSlug($name);
        $description = $data['description'] ?? '';
        $price = $data['price'] ?? 0;
        $setup_fee = $data['setup_fee'] ?? 0;
        $recurring = $data['recurring'] ?? 0;
        $billing_cycle = $data['billing_cycle'] ?? 'monthly';
        $status = $data['status'] ?? 'active';
        
        // Nouveaux champs
        $short_description = $data['short_description'] ?? '';
        $image_url = $data['image_url'] ?? '';
        $color = $data['color'] ?? '#0066ff';
        $welcome_email_id = $data['welcome_email_id'] ?? null;
        $hidden = isset($data['hidden']) ? (int)$data['hidden'] : 0;
        $featured = isset($data['featured']) ? (int)$data['featured'] : 0;
        $stock_control = isset($data['stock_control']) ? (int)$data['stock_control'] : 0;
        $stock_quantity = $data['stock_quantity'] ?? 0;
        // Assurer que product_type garde la valeur envoyée
        $product_type = isset($data['product_type']) && !empty($data['product_type']) ? $data['product_type'] : 'shared_hosting';
        $group_id = $data['group_id'] ?? null;
        $features = json_encode($data['features'] ?? []);
        $options = json_encode($data['options'] ?? []);
        $created_at = date('Y-m-d H:i:s');
        
        // Champs de provisionnement automatique
        $auto_provision = $data['auto_provision'] ?? 0;
        
        // CORRECTION: Assurer que provisioning_type garde la valeur envoyée par le frontend
        // Ce champ doit représenter le TYPE d'auto-provisionnement (auto_setup_on_order, auto_setup_on_payment, etc.)
        // et NON le nom du module (cpanel, proxmox, etc.)
        $provisioning_type = isset($data['provisioning_type']) && !empty($data['provisioning_type']) 
            ? $data['provisioning_type'] 
            : ($auto_provision ? 'auto_setup_on_order' : 'manual');
        
        Logger::channel('api')->debug("[Product Model] Type de provisionnement défini: {$provisioning_type}");
        
        // Assurer que server_type est correctement défini
        $server_type = isset($data['server_type']) && !empty($data['server_type']) ? $data['server_type'] : ($data['module'] ?? '');
        
        // S'assurer que package_name est une valeur valide
        $package_name = isset($data['package_name']) && !empty($data['package_name']) ? $data['package_name'] : '';

        // Champs de prix par cycle (approche WHMCS)
        $price_monthly = $data['price_monthly'] ?? 0;
        $price_quarterly = $data['price_quarterly'] ?? 0;
        $price_semiannually = $data['price_semiannually'] ?? 0;
        $price_annually = $data['price_annually'] ?? 0;
        $price_biennially = $data['price_biennially'] ?? 0;
        $price_triennially = $data['price_triennially'] ?? 0;

        // Setup fees par cycle
        $setup_fee_monthly = $data['setup_fee_monthly'] ?? 0;
        $setup_fee_quarterly = $data['setup_fee_quarterly'] ?? 0;
        $setup_fee_semiannually = $data['setup_fee_semiannually'] ?? 0;
        $setup_fee_annually = $data['setup_fee_annually'] ?? 0;
        $setup_fee_biennially = $data['setup_fee_biennially'] ?? 0;
        $setup_fee_triennially = $data['setup_fee_triennially'] ?? 0;

        // Configuration des cycles
        $available_cycles = $data['available_cycles'] ?? 'monthly';
        $default_cycle = $data['default_cycle'] ?? 'monthly';
        $multiple_cycles_enabled = isset($data['multiple_cycles_enabled']) ? (int)$data['multiple_cycles_enabled'] : 0;

        // Debug des valeurs préparées pour l'insertion
        Logger::channel('api')->debug('[Product Model] Valeurs prêtes pour insertion', [
            'name' => $name,
            'product_type' => $product_type,
            'provisioning_type' => $provisioning_type,
            'server_type' => $server_type,
            'package_name' => $package_name,
            'multiple_cycles_enabled' => $multiple_cycles_enabled,
            'available_cycles' => $available_cycles
        ]);

        $query = "INSERT INTO {$this->table} (
                name, slug, description, short_description, image_url, color, welcome_email_id, hidden, featured,
                stock_control, stock_quantity, price, setup_fee, recurring, billing_cycle,
                price_monthly, price_quarterly, price_semiannually, price_annually, price_biennially, price_triennially,
                setup_fee_monthly, setup_fee_quarterly, setup_fee_semiannually, setup_fee_annually, setup_fee_biennially, setup_fee_triennially,
                available_cycles, default_cycle, multiple_cycles_enabled,
                status, product_type, group_id, features, options, created_at,
                auto_provision, provisioning_type, server_type, package_name
            ) VALUES (
                :name, :slug, :description, :short_description, :image_url, :color, :welcome_email_id, :hidden, :featured,
                :stock_control, :stock_quantity, :price, :setup_fee, :recurring, :billing_cycle,
                :price_monthly, :price_quarterly, :price_semiannually, :price_annually, :price_biennially, :price_triennially,
                :setup_fee_monthly, :setup_fee_quarterly, :setup_fee_semiannually, :setup_fee_annually, :setup_fee_biennially, :setup_fee_triennially,
                :available_cycles, :default_cycle, :multiple_cycles_enabled,
                :status, :product_type, :group_id, :features, :options, :created_at,
                :auto_provision, :provisioning_type, :server_type, :package_name
            )";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':slug', $slug);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':short_description', $short_description);
        $stmt->bindParam(':image_url', $image_url);
        $stmt->bindParam(':color', $color);
        $stmt->bindParam(':welcome_email_id', $welcome_email_id);
        $stmt->bindParam(':hidden', $hidden);
        $stmt->bindParam(':featured', $featured);
        $stmt->bindParam(':stock_control', $stock_control);
        $stmt->bindParam(':stock_quantity', $stock_quantity);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':setup_fee', $setup_fee);
        $stmt->bindParam(':recurring', $recurring);
        $stmt->bindParam(':billing_cycle', $billing_cycle);

        // Bind des prix par cycle
        $stmt->bindParam(':price_monthly', $price_monthly);
        $stmt->bindParam(':price_quarterly', $price_quarterly);
        $stmt->bindParam(':price_semiannually', $price_semiannually);
        $stmt->bindParam(':price_annually', $price_annually);
        $stmt->bindParam(':price_biennially', $price_biennially);
        $stmt->bindParam(':price_triennially', $price_triennially);

        // Bind des setup fees par cycle
        $stmt->bindParam(':setup_fee_monthly', $setup_fee_monthly);
        $stmt->bindParam(':setup_fee_quarterly', $setup_fee_quarterly);
        $stmt->bindParam(':setup_fee_semiannually', $setup_fee_semiannually);
        $stmt->bindParam(':setup_fee_annually', $setup_fee_annually);
        $stmt->bindParam(':setup_fee_biennially', $setup_fee_biennially);
        $stmt->bindParam(':setup_fee_triennially', $setup_fee_triennially);

        // Bind de la configuration des cycles
        $stmt->bindParam(':available_cycles', $available_cycles);
        $stmt->bindParam(':default_cycle', $default_cycle);
        $stmt->bindParam(':multiple_cycles_enabled', $multiple_cycles_enabled);

        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':product_type', $product_type);
        $stmt->bindParam(':group_id', $group_id);
        $stmt->bindParam(':features', $features);
        $stmt->bindParam(':options', $options);
        $stmt->bindParam(':created_at', $created_at);
        $stmt->bindParam(':auto_provision', $auto_provision);
        $stmt->bindParam(':provisioning_type', $provisioning_type);
        $stmt->bindParam(':server_type', $server_type);
        $stmt->bindParam(':package_name', $package_name);
        
        try {
            if ($stmt->execute()) {
                $newId = $this->db->lastInsertId();
                Logger::channel('api')->info("[Product Model] Produit créé avec succès", ['id' => $newId]);
                return $this->getById($newId);
            } else {
                Logger::channel('api')->error("[Product Model] Erreur lors de l'exécution de la requête SQL", ['errorInfo' => $stmt->errorInfo()]);
            }
        } catch (\PDOException $e) {
            Logger::channel('api')->error("[Product Model] Exception PDO: " . $e->getMessage(), ['exception' => $e]);
        } catch (\Exception $e) {
            Logger::channel('api')->error("[Product Model] Exception générale: " . $e->getMessage(), ['exception' => $e]);
        }
        
        return false;
    }

    /**
     * Met à jour un produit existant
     */
    public function update($id, $data) {
        // Log détaillé des données reçues
        Logger::channel('api')->info("[Product Model] === DÉBUT MISE À JOUR PRODUIT ===", [
            'id' => $id,
            'data_received' => $data,
            'data_types' => array_map('gettype', $data),
            'boolean_fields' => [
                'recurring' => isset($data['recurring']) ? ['value' => $data['recurring'], 'type' => gettype($data['recurring'])] : 'not_set',
                'multiple_cycles_enabled' => isset($data['multiple_cycles_enabled']) ? ['value' => $data['multiple_cycles_enabled'], 'type' => gettype($data['multiple_cycles_enabled'])] : 'not_set',
                'hidden' => isset($data['hidden']) ? ['value' => $data['hidden'], 'type' => gettype($data['hidden'])] : 'not_set',
                'featured' => isset($data['featured']) ? ['value' => $data['featured'], 'type' => gettype($data['featured'])] : 'not_set'
            ]
        ]);

        $product = $this->getById($id);

        if (!$product) {
            Logger::channel('api')->error("[Product Model] Produit non trouvé pour mise à jour", ['id' => $id]);
            return false;
        }

        $name = $data['name'] ?? $product['name'];
        $slug = $data['slug'] ?? $product['slug'];
        $description = $data['description'] ?? $product['description'];
        
        // Nouveaux champs ajoutés
        $short_description = $data['short_description'] ?? $product['short_description'] ?? '';
        $image_url = $data['image_url'] ?? $product['image_url'] ?? '';
        $color = $data['color'] ?? $product['color'] ?? '#0066ff';
        $welcome_email_id = $data['welcome_email_id'] ?? $product['welcome_email_id'] ?? null;
        $hidden = isset($data['hidden']) ? (int)$data['hidden'] : (int)($product['hidden'] ?? 0);
        $featured = isset($data['featured']) ? (int)$data['featured'] : (int)($product['featured'] ?? 0);
        $stock_control = isset($data['stock_control']) ? (int)$data['stock_control'] : (int)($product['stock_control'] ?? 0);
        $stock_quantity = $data['stock_quantity'] ?? $product['stock_quantity'] ?? 0;
        
        $price = $data['price'] ?? $product['price'];
        $setup_fee = $data['setup_fee'] ?? $product['setup_fee'];
        $recurring = isset($data['recurring']) ? (int)(bool)$data['recurring'] : (int)($product['recurring'] ?? 0);
        $billing_cycle = $data['billing_cycle'] ?? $product['billing_cycle'];
        $status = $data['status'] ?? $product['status'];
        $product_type = $data['product_type'] ?? $product['product_type'];
        $group_id = $data['group_id'] ?? $product['group_id'];
        $features = isset($data['features']) ? json_encode($data['features']) : $product['features'];
        $options = isset($data['options']) ? json_encode($data['options']) : $product['options'];
        $updated_at = date('Y-m-d H:i:s');
        
        // Debug des données reçues pour la mise à jour
        Logger::channel('api')->debug("[Product Model] Données reçues pour mise à jour ID {$id}", ['data' => $data]);
        
        // Champs de provisionnement automatique
        $auto_provision = $data['auto_provision'] ?? $product['auto_provision'] ?? 0;
        
        // S'assurer que provisioning_type garde la valeur envoyée
        $provisioning_type = isset($data['provisioning_type']) && !empty($data['provisioning_type']) 
            ? $data['provisioning_type'] 
            : ($product['provisioning_type'] ?? 'manual');
        
        // S'assurer que server_type est correctement défini
        $server_type = isset($data['server_type']) && !empty($data['server_type']) 
            ? $data['server_type'] 
            : (isset($data['module']) && !empty($data['module']) ? $data['module'] : ($product['server_type'] ?? ''));
        
        // S'assurer que package_name est une valeur valide
        $package_name = isset($data['package_name']) && !empty($data['package_name'])
            ? $data['package_name']
            : ($product['package_name'] ?? '');

        // Champs de prix par cycle (approche WHMCS)
        $price_monthly = $data['price_monthly'] ?? $product['price_monthly'] ?? 0;
        $price_quarterly = $data['price_quarterly'] ?? $product['price_quarterly'] ?? 0;
        $price_semiannually = $data['price_semiannually'] ?? $product['price_semiannually'] ?? 0;
        $price_annually = $data['price_annually'] ?? $product['price_annually'] ?? 0;
        $price_biennially = $data['price_biennially'] ?? $product['price_biennially'] ?? 0;
        $price_triennially = $data['price_triennially'] ?? $product['price_triennially'] ?? 0;

        // Setup fees par cycle
        $setup_fee_monthly = $data['setup_fee_monthly'] ?? $product['setup_fee_monthly'] ?? 0;
        $setup_fee_quarterly = $data['setup_fee_quarterly'] ?? $product['setup_fee_quarterly'] ?? 0;
        $setup_fee_semiannually = $data['setup_fee_semiannually'] ?? $product['setup_fee_semiannually'] ?? 0;
        $setup_fee_annually = $data['setup_fee_annually'] ?? $product['setup_fee_annually'] ?? 0;
        $setup_fee_biennially = $data['setup_fee_biennially'] ?? $product['setup_fee_biennially'] ?? 0;
        $setup_fee_triennially = $data['setup_fee_triennially'] ?? $product['setup_fee_triennially'] ?? 0;

        // Configuration des cycles
        $available_cycles = $data['available_cycles'] ?? $product['available_cycles'] ?? 'monthly';
        $default_cycle = $data['default_cycle'] ?? $product['default_cycle'] ?? 'monthly';
        $multiple_cycles_enabled = isset($data['multiple_cycles_enabled']) ? (int)$data['multiple_cycles_enabled'] : (int)($product['multiple_cycles_enabled'] ?? 0);

        // Log spécifique pour les champs de pricing après conversion
        Logger::channel('api')->info('[Product Model] === VALEURS APRÈS CONVERSION ===', [
            'pricing_fields' => [
                'price_monthly' => $price_monthly,
                'price_quarterly' => $price_quarterly,
                'price_semiannually' => $price_semiannually,
                'price_annually' => $price_annually,
                'price_biennially' => $price_biennially,
                'price_triennially' => $price_triennially,
                'setup_fee_monthly' => $setup_fee_monthly,
                'setup_fee_quarterly' => $setup_fee_quarterly,
                'setup_fee_semiannually' => $setup_fee_semiannually,
                'setup_fee_annually' => $setup_fee_annually,
                'setup_fee_biennially' => $setup_fee_biennially,
                'setup_fee_triennially' => $setup_fee_triennially
            ],
            'boolean_fields_converted' => [
                'recurring' => $recurring,
                'multiple_cycles_enabled' => $multiple_cycles_enabled,
                'hidden' => $hidden,
                'featured' => $featured
            ],
            'cycle_config' => [
                'available_cycles' => $available_cycles,
                'default_cycle' => $default_cycle
            ]
        ]);

        // Debug des valeurs préparées pour la mise à jour
        Logger::channel('api')->debug('[Product Model] Valeurs prêtes pour mise à jour', [
            'name' => $name,
            'product_type' => $product_type,
            'provisioning_type' => $provisioning_type,
            'server_type' => $server_type,
            'package_name' => $package_name,
            'multiple_cycles_enabled' => $multiple_cycles_enabled,
            'available_cycles' => $available_cycles
        ]);

        // Log des valeurs des nouveaux champs avant mise à jour
        Logger::channel('api')->debug('[Product Model] Valeurs des nouveaux champs pour mise à jour', [
            'short_description' => $short_description,
            'image_url' => $image_url,
            'color' => $color,
            'welcome_email_id' => $welcome_email_id,
            'hidden' => $hidden,
            'featured' => $featured,
            'stock_control' => $stock_control,
            'stock_quantity' => $stock_quantity,
        ]);
        
        $query = "UPDATE {$this->table} SET 
                  name = :name, 
                  slug = :slug, 
                  description = :description, 
                  short_description = :short_description,
                  image_url = :image_url,
                  color = :color,
                  welcome_email_id = :welcome_email_id,
                  hidden = :hidden,
                  featured = :featured,
                  stock_control = :stock_control,
                  stock_quantity = :stock_quantity,
                  price = :price, 
                  setup_fee = :setup_fee, 
                  recurring = :recurring, 
                  billing_cycle = :billing_cycle,
                  price_monthly = :price_monthly,
                  price_quarterly = :price_quarterly,
                  price_semiannually = :price_semiannually,
                  price_annually = :price_annually,
                  price_biennially = :price_biennially,
                  price_triennially = :price_triennially,
                  setup_fee_monthly = :setup_fee_monthly,
                  setup_fee_quarterly = :setup_fee_quarterly,
                  setup_fee_semiannually = :setup_fee_semiannually,
                  setup_fee_annually = :setup_fee_annually,
                  setup_fee_biennially = :setup_fee_biennially,
                  setup_fee_triennially = :setup_fee_triennially,
                  available_cycles = :available_cycles,
                  default_cycle = :default_cycle,
                  multiple_cycles_enabled = :multiple_cycles_enabled,
                  status = :status,
                  product_type = :product_type,
                  group_id = :group_id,
                  features = :features,
                  options = :options,
                  updated_at = :updated_at,
                  auto_provision = :auto_provision,
                  provisioning_type = :provisioning_type,
                  server_type = :server_type,
                  package_name = :package_name
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':slug', $slug);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':short_description', $short_description);
        $stmt->bindParam(':image_url', $image_url);
        $stmt->bindParam(':color', $color);
        $stmt->bindParam(':welcome_email_id', $welcome_email_id);
        $stmt->bindParam(':hidden', $hidden);
        $stmt->bindParam(':featured', $featured);
        $stmt->bindParam(':stock_control', $stock_control);
        $stmt->bindParam(':stock_quantity', $stock_quantity);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':setup_fee', $setup_fee);
        $stmt->bindParam(':recurring', $recurring);
        $stmt->bindParam(':billing_cycle', $billing_cycle);

        // Bind des prix par cycle
        $stmt->bindParam(':price_monthly', $price_monthly);
        $stmt->bindParam(':price_quarterly', $price_quarterly);
        $stmt->bindParam(':price_semiannually', $price_semiannually);
        $stmt->bindParam(':price_annually', $price_annually);
        $stmt->bindParam(':price_biennially', $price_biennially);
        $stmt->bindParam(':price_triennially', $price_triennially);

        // Bind des setup fees par cycle
        $stmt->bindParam(':setup_fee_monthly', $setup_fee_monthly);
        $stmt->bindParam(':setup_fee_quarterly', $setup_fee_quarterly);
        $stmt->bindParam(':setup_fee_semiannually', $setup_fee_semiannually);
        $stmt->bindParam(':setup_fee_annually', $setup_fee_annually);
        $stmt->bindParam(':setup_fee_biennially', $setup_fee_biennially);
        $stmt->bindParam(':setup_fee_triennially', $setup_fee_triennially);

        // Bind de la configuration des cycles
        $stmt->bindParam(':available_cycles', $available_cycles);
        $stmt->bindParam(':default_cycle', $default_cycle);
        $stmt->bindParam(':multiple_cycles_enabled', $multiple_cycles_enabled);

        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':product_type', $product_type);
        $stmt->bindParam(':group_id', $group_id);
        $stmt->bindParam(':features', $features);
        $stmt->bindParam(':options', $options);
        $stmt->bindParam(':updated_at', $updated_at);
        $stmt->bindParam(':auto_provision', $auto_provision);
        $stmt->bindParam(':provisioning_type', $provisioning_type);
        $stmt->bindParam(':server_type', $server_type);
        $stmt->bindParam(':package_name', $package_name);
        $stmt->bindParam(':id', $id);
        
        try {
            if ($stmt->execute()) {
                Logger::channel('api')->info("[Product Model] Produit mis à jour avec succès", ['id' => $id]);
                return $this->getById($id);
            } else {
                Logger::channel('api')->error("[Product Model] Erreur lors de la mise à jour SQL", ['errorInfo' => $stmt->errorInfo()]);
            }
        } catch (\PDOException $e) {
            Logger::channel('api')->error("[Product Model] Exception PDO lors de la mise à jour: " . $e->getMessage(), ['exception' => $e]);
        } catch (\Exception $e) {
            Logger::channel('api')->error("[Product Model] Exception générale lors de la mise à jour: " . $e->getMessage(), ['exception' => $e]);
        }
        
        return false;
    }

    /**
     * Supprime un produit (suppression physique)
     */
    public function delete($id) {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }

    /**
     * Récupère les cycles de facturation disponibles pour un produit
     */
    public function getAvailableBillingCycles($productId) {
        $product = $this->getById($productId);
        if (!$product) {
            return [];
        }

        $availableCycles = explode(',', $product['available_cycles']);
        $cycles = [];

        foreach ($availableCycles as $cycle) {
            $priceField = "price_{$cycle}";
            $setupField = "setup_fee_{$cycle}";

            if (isset($product[$priceField]) && $product[$priceField] > 0) {
                $cycles[] = [
                    'cycle' => $cycle,
                    'price' => $product[$priceField],
                    'setup_fee' => $product[$setupField] ?? $product['setup_fee'], // Fallback
                    'is_default' => ($product['default_cycle'] === $cycle),
                    'display_name' => $this->getCycleDisplayName($cycle)
                ];
            }
        }

        // Trier par ordre de préférence (défaut en premier)
        usort($cycles, function($a, $b) {
            if ($a['is_default']) return -1;
            if ($b['is_default']) return 1;
            return 0;
        });

        return $cycles;
    }

    /**
     * Récupère le prix pour un cycle spécifique
     */
    public function getPriceForCycle($productId, $cycle) {
        $product = $this->getById($productId);
        if (!$product) {
            return null;
        }

        $priceField = "price_{$cycle}";
        $setupField = "setup_fee_{$cycle}";

        if (isset($product[$priceField])) {
            return [
                'price' => $product[$priceField],
                'setup_fee' => $product[$setupField] ?? $product['setup_fee'],
                'cycle' => $cycle
            ];
        }

        return null;
    }

    /**
     * Vérifie si un cycle est disponible pour un produit
     */
    public function isCycleAvailable($productId, $cycle) {
        $product = $this->getById($productId);
        if (!$product) {
            return false;
        }

        $availableCycles = explode(',', $product['available_cycles']);
        $priceField = "price_{$cycle}";

        return in_array($cycle, $availableCycles) &&
               isset($product[$priceField]) &&
               $product[$priceField] > 0;
    }

    /**
     * Récupère les produits avec un cycle spécifique disponible
     */
    public function getProductsWithCycle($cycle) {
        $query = "SELECT * FROM {$this->table}
                  WHERE FIND_IN_SET(:cycle, available_cycles)
                  AND price_{$cycle} > 0
                  AND status = 'active'";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':cycle', $cycle);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Retourne le nom d'affichage d'un cycle
     */
    private function getCycleDisplayName($cycle) {
        $names = [
            'monthly' => 'Mensuel',
            'quarterly' => 'Trimestriel',
            'semi_annually' => 'Semestriel',
            'annually' => 'Annuel',
            'biennially' => 'Biennal',
            'triennially' => 'Triennal'
        ];

        return $names[$cycle] ?? ucfirst($cycle);
    }

    /**
     * Génère un slug à partir d'un nom
     */
    private function generateSlug($name) {
        $slug = strtolower($name);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug;
    }
}

<?php

namespace TechCMS\Api\V1\Controllers\Admin\Product;

use TechCMS\Api\V1\Controllers\Admin\AdminBaseController;
use TechCMS\Common\Core\Database;
use TechCMS\Common\Core\ModuleManager;
use TechCMS\Common\Core\Logger;
use TechCMS\Admin\Models\AdminServerModel;
use TechCMS\Api\V1\Models\Server;

/**
 * Contrôleur pour la gestion des modules de produits (style WHMCS)
 */
class AdminProductModuleController extends AdminBaseController
{
    private $moduleManager;
    private $db;
    private $serverModel;

    public function __construct()
    {
        parent::__construct();
        $this->moduleManager = ModuleManager::getInstance();
        $this->db = Database::getInstance();
        $this->serverModel = new AdminServerModel();
    }

    /**
     * Récupère les options de configuration pour un produit (style WHMCS ConfigOptions)
     * 
     * @return array Réponse API
     */
    public function getConfigOptions() {
        $productId = $_GET['product_id'] ?? null;
        $moduleType = $_GET['module_type'] ?? 'servers';
        $moduleName = $_GET['module_name'] ?? '';

        if (!$productId) {
            return $this->sendError('ID produit requis');
        }

        try {
            // Récupérer le produit
            $query = "SELECT * FROM products WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$productId]);
            $product = $stmt->fetch();

            if (!$product) {
                return $this->sendError('Produit non trouvé');
            }

            // Configuration de base TOUJOURS disponible (style WHMCS)
            $configOptions = [
                'server_id' => [
                    'type' => 'dropdown',
                    'name' => 'Serveur',
                    'options' => $this->getAvailableServers($moduleType, $moduleName),
                    'description' => 'Sélectionnez un serveur'
                ],
                'plan' => [
                    'type' => 'dropdown', 
                    'name' => 'Plan',
                    'options' => [], // Vide au début
                    'description' => 'Sélectionnez un plan'
                ]
            ];

            // Si un serveur est déjà sélectionné, enrichir dynamiquement (style WHMCS)
            if (!empty($product['server_id']) && !empty($moduleName)) {
                $enrichedOptions = $this->enrichConfigOptions($product['server_id'], $moduleName, $configOptions);
                if ($enrichedOptions) {
                    $configOptions = $enrichedOptions;
                }
            }

            return $this->sendResponse([
                'success' => true,
                'message' => 'Options de configuration récupérées',
                'data' => $configOptions
            ], 200);

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de la récupération des options de configuration", ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors de la récupération des options de configuration');
        }
    }

    /**
     * Récupère les serveurs disponibles pour un type de module
     * 
     * @param string $moduleType Type de module (servers, gateways, etc.)
     * @param string $moduleName Nom du module
     * @return array Liste des serveurs
     */
    private function getAvailableServers($moduleType, $moduleName) {
        try {
            $query = "SELECT id, name, hostname FROM servers WHERE type = ? AND module_name = ? AND active = 1";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$moduleType, $moduleName]);
            $servers = $stmt->fetchAll();

            $serverOptions = [];
            foreach ($servers as $server) {
                $serverOptions[$server['id']] = $server['id'] . ' - ' . $server['name'];
            }

            return $serverOptions;
        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de la récupération des serveurs", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Enrichit les options de configuration avec les données du serveur (style WHMCS)
     * 
     * @param int $serverId ID du serveur
     * @param string $moduleName Nom du module
     * @param array $baseOptions Options de base
     * @return array|null Options enrichies ou null si échec
     */
    private function enrichConfigOptions($serverId, $moduleName, $baseOptions) {
        try {
            // Récupérer la configuration du serveur
            $query = "SELECT * FROM servers WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$serverId]);
            $server = $stmt->fetch();

            if (!$server) {
                return null;
            }

            // Charger le module
            $moduleInstance = $this->moduleManager->loadModule('servers', $moduleName);
            if (!$moduleInstance) {
                return null; // Retourner null au lieu d'exception (style WHMCS)
            }

            // Configurer le module
            $config = json_decode($server['config'], true) ?? [];
            if (method_exists($moduleInstance, 'initialize')) {
                $initResult = $moduleInstance->initialize($config);
                if (!$initResult) {
                    return null; // Retourner null au lieu d'exception (style WHMCS)
                }
            }

            // Récupérer les plans du serveur
            $plans = [];
            if (method_exists($moduleInstance, 'getAvailablePlans')) {
                $plans = $moduleInstance->getAvailablePlans();
            }

            // Enrichir les options avec les plans
            if (!empty($plans)) {
                $planOptions = [];
                foreach ($plans as $plan) {
                    $planOptions[$plan['id']] = $plan['id'] . ' - ' . $plan['name'];
                }
                $baseOptions['plan']['options'] = $planOptions;
            }

            // Ajouter d'autres options spécifiques au module
            if (method_exists($moduleInstance, 'getConfigurationFields')) {
                $moduleFields = $moduleInstance->getConfigurationFields();
                foreach ($moduleFields as $fieldName => $fieldConfig) {
                    $baseOptions[$fieldName] = $fieldConfig;
                }
            }

            return $baseOptions;

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de l'enrichissement des options", ['error' => $e->getMessage()]);
            return null; // Retourner null au lieu d'exception (style WHMCS)
        }
    }

    /**
     * Récupère les plans pour un module spécifique (style WHMCS)
     * 
     * @return array Réponse API
     */
    public function getModulePlans() {
        $serverId = $_GET['server_id'] ?? null;
        $moduleName = $_GET['module_name'] ?? '';

        Logger::channel('api')->info('[getModulePlans] Début de la récupération des plans', [
            'server_id' => $serverId,
            'module_name' => $moduleName
        ]);

        if (!$serverId || !$moduleName) {
            Logger::channel('api')->error('[getModulePlans] Paramètres manquants', [
                'server_id' => $serverId,
                'module_name' => $moduleName
            ]);
            return $this->sendError('ID serveur et nom de module requis');
        }

        try {
            // Récupérer la configuration complète et déchiffrée du serveur
            Logger::channel('api')->info('[getModulePlans] Récupération de la configuration du serveur', [
                'server_id' => $serverId
            ]);

            $config = $this->serverModel->getFullDecryptedConfig($serverId);

            Logger::channel('api')->info('[getModulePlans] Configuration récupérée', [
                'config_exists' => !empty($config),
                'config_keys' => $config ? array_keys($config) : [],
                'server_url' => $config['serverUrl'] ?? 'NON_DEFINI',
                'username' => $config['username'] ?? 'NON_DEFINI',
                'has_api_token' => !empty($config['api_token']),
                'has_password' => !empty($config['password']),
                'has_access_hash' => !empty($config['accessHash']),
                'secure' => $config['secure'] ?? 'NON_DEFINI'
            ]);

            if (!$config) {
                Logger::channel('api')->error('[getModulePlans] Configuration du serveur introuvable', [
                    'server_id' => $serverId
                ]);
                return $this->sendResponse([
                    'success' => true,
                    'message' => 'Serveur non trouvé ou configuration inaccessible',
                    'data' => []
                ], 200);
            }

            // Charger le module
            Logger::channel('api')->info('[getModulePlans] Chargement du module', [
                'module_name' => $moduleName
            ]);

            $moduleInstance = $this->moduleManager->loadModule('servers', $moduleName);

            Logger::channel('api')->info('[getModulePlans] Module chargé', [
                'module_loaded' => !empty($moduleInstance),
                'module_class' => $moduleInstance ? get_class($moduleInstance) : 'NULL'
            ]);

            if (!$moduleInstance) {
                Logger::channel('api')->error('[getModulePlans] Module non trouvé', [
                    'module_name' => $moduleName
                ]);
                return $this->sendResponse([
                    'success' => true,
                    'message' => 'Module non trouvé',
                    'data' => []
                ], 200);
            }

            // Initialiser le module avec la configuration déchiffrée
            Logger::channel('api')->info('[getModulePlans] Initialisation du module', [
                'has_initialize_method' => method_exists($moduleInstance, 'initialize'),
                'config_for_init' => [
                    'serverUrl' => $config['serverUrl'] ?? 'NON_DEFINI',
                    'username' => $config['username'] ?? 'NON_DEFINI',
                    'has_api_token' => !empty($config['api_token']),
                    'has_password' => !empty($config['password']),
                    'has_access_hash' => !empty($config['accessHash']),
                    'secure' => $config['secure'] ?? 'NON_DEFINI'
                ]
            ]);

            if (method_exists($moduleInstance, 'initialize')) {
                $initResult = $moduleInstance->initialize($config);
                Logger::channel('api')->info('[getModulePlans] Module initialisé', [
                    'init_result' => $initResult
                ]);
            }

            $plans = [];

            // Recherche de la méthode appropriée pour récupérer les plans/packages
            Logger::channel('api')->info('[getModulePlans] Recherche des méthodes de récupération de plans', [
                'has_listPackages' => method_exists($moduleInstance, 'listPackages'),
                'has_getAvailablePlans' => method_exists($moduleInstance, 'getAvailablePlans'),
                'has_getPlans' => method_exists($moduleInstance, 'getPlans'),
                'has_getPackages' => method_exists($moduleInstance, 'getPackages')
            ]);

            // Utilise la même logique que AdminModuleController pour la compatibilité
            if (method_exists($moduleInstance, 'listPackages')) {
                Logger::channel('api')->info('[getModulePlans] Appel de listPackages()');
                try {
                    $plans = $moduleInstance->listPackages();
                    Logger::channel('api')->info('[getModulePlans] listPackages() exécuté', [
                        'plans_count' => count($plans),
                        'plans_preview' => array_slice($plans, 0, 3)
                    ]);
                } catch (\Exception $e) {
                    Logger::channel('api')->error('[getModulePlans] Erreur dans listPackages()', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            } elseif (method_exists($moduleInstance, 'getAvailablePlans')) {
                Logger::channel('api')->info('[getModulePlans] Appel de getAvailablePlans()');
                try {
                    $plans = $moduleInstance->getAvailablePlans();
                    Logger::channel('api')->info('[getModulePlans] getAvailablePlans() exécuté', [
                        'plans_count' => count($plans)
                    ]);
                } catch (\Exception $e) {
                    Logger::channel('api')->error('[getModulePlans] Erreur dans getAvailablePlans()', [
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            } elseif (method_exists($moduleInstance, 'getPlans')) {
                Logger::channel('api')->info('[getModulePlans] Appel de getPlans()');
                try {
                    $plans = $moduleInstance->getPlans();
                    Logger::channel('api')->info('[getModulePlans] getPlans() exécuté', [
                        'plans_count' => count($plans)
                    ]);
                } catch (\Exception $e) {
                    Logger::channel('api')->error('[getModulePlans] Erreur dans getPlans()', [
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            } elseif (method_exists($moduleInstance, 'getPackages')) {
                Logger::channel('api')->info('[getModulePlans] Appel de getPackages()');
                try {
                    $plans = $moduleInstance->getPackages();
                    Logger::channel('api')->info('[getModulePlans] getPackages() exécuté', [
                        'plans_count' => count($plans)
                    ]);
                } catch (\Exception $e) {
                    Logger::channel('api')->error('[getModulePlans] Erreur dans getPackages()', [
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            } else {
                Logger::channel('api')->warning('[getModulePlans] Aucune méthode de récupération de plans trouvée');
            }

            Logger::channel('api')->info('[getModulePlans] Résultat final', [
                'plans_count' => count($plans),
                'plans_data' => $plans
            ]);

            return $this->sendResponse([
                'success' => true,
                'message' => 'Plans récupérés avec succès',
                'data' => $plans
            ], 200);

        } catch (\Exception $e) {
            Logger::channel('api')->error('[getModulePlans] Exception capturée', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'server_id' => $serverId,
                'module_name' => $moduleName
            ]);

            // Style WHMCS : retourner un tableau vide au lieu d'une erreur
            return $this->sendResponse([
                'success' => true,
                'message' => 'Erreur lors de la récupération des plans: ' . $e->getMessage(),
                'data' => []
            ], 200);
        }
    }

    /**
     * Test de connexion à un serveur (style WHMCS TestConnection)
     * 
     * @return array Réponse API
     */
    public function testConnection() {
        $serverId = $_POST['server_id'] ?? null;
        $moduleName = $_POST['module_name'] ?? '';

        if (!$serverId || !$moduleName) {
            return $this->sendError('ID serveur et nom de module requis');
        }

        try {
            // Récupérer la configuration du serveur
            $query = "SELECT * FROM servers WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$serverId]);
            $server = $stmt->fetch();

            if (!$server) {
                return $this->sendError('Serveur non trouvé');
            }

            // Charger le module
            $moduleInstance = $this->moduleManager->loadModule('servers', $moduleName);
            if (!$moduleInstance) {
                return $this->sendError('Module non trouvé');
            }

            // Tester la connexion
            $config = json_decode($server['config'], true) ?? [];
            if (method_exists($moduleInstance, 'testConnection')) {
                $result = $moduleInstance->testConnection($config);
                
                return $this->sendResponse([
                    'success' => $result['success'] ?? false,
                    'message' => $result['message'] ?? 'Test de connexion effectué',
                    'data' => $result
                ], 200);
            } else {
                return $this->sendError('Le module ne supporte pas le test de connexion');
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors du test de connexion", ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors du test de connexion: ' . $e->getMessage());
        }
    }
}

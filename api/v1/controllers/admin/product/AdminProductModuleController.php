<?php

namespace TechCMS\Api\V1\Controllers\Admin\Product;

use TechCMS\Api\V1\Controllers\Admin\AdminBaseController;
use TechCMS\Common\Core\Database;
use TechCMS\Common\Core\ModuleManager;
use TechCMS\Common\Core\Logger;

/**
 * Contrôleur pour la gestion des modules de produits (style WHMCS)
 */
class AdminProductModuleController extends AdminBaseController
{
    private $moduleManager;
    private $db;

    public function __construct()
    {
        parent::__construct();
        $this->moduleManager = ModuleManager::getInstance();
        $this->db = Database::getInstance();
    }

    /**
     * Récupère les options de configuration pour un produit (style WHMCS ConfigOptions)
     * 
     * @return array Réponse API
     */
    public function getConfigOptions() {
        $productId = $_GET['product_id'] ?? null;
        $moduleType = $_GET['module_type'] ?? 'servers';
        $moduleName = $_GET['module_name'] ?? '';

        if (!$productId) {
            return $this->sendError('ID produit requis');
        }

        try {
            // Récupérer le produit
            $query = "SELECT * FROM products WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$productId]);
            $product = $stmt->fetch();

            if (!$product) {
                return $this->sendError('Produit non trouvé');
            }

            // Configuration de base TOUJOURS disponible (style WHMCS)
            $configOptions = [
                'server_id' => [
                    'type' => 'dropdown',
                    'name' => 'Serveur',
                    'options' => $this->getAvailableServers($moduleType, $moduleName),
                    'description' => 'Sélectionnez un serveur'
                ],
                'plan' => [
                    'type' => 'dropdown', 
                    'name' => 'Plan',
                    'options' => [], // Vide au début
                    'description' => 'Sélectionnez un plan'
                ]
            ];

            // Si un serveur est déjà sélectionné, enrichir dynamiquement (style WHMCS)
            if (!empty($product['server_id']) && !empty($moduleName)) {
                $enrichedOptions = $this->enrichConfigOptions($product['server_id'], $moduleName, $configOptions);
                if ($enrichedOptions) {
                    $configOptions = $enrichedOptions;
                }
            }

            return $this->sendResponse([
                'success' => true,
                'message' => 'Options de configuration récupérées',
                'data' => $configOptions
            ], 200);

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de la récupération des options de configuration", ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors de la récupération des options de configuration');
        }
    }

    /**
     * Récupère les serveurs disponibles pour un type de module
     * 
     * @param string $moduleType Type de module (servers, gateways, etc.)
     * @param string $moduleName Nom du module
     * @return array Liste des serveurs
     */
    private function getAvailableServers($moduleType, $moduleName) {
        try {
            $query = "SELECT id, name, hostname FROM servers WHERE type = ? AND module_name = ? AND active = 1";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$moduleType, $moduleName]);
            $servers = $stmt->fetchAll();

            $serverOptions = [];
            foreach ($servers as $server) {
                $serverOptions[$server['id']] = $server['id'] . ' - ' . $server['name'];
            }

            return $serverOptions;
        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de la récupération des serveurs", ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Enrichit les options de configuration avec les données du serveur (style WHMCS)
     * 
     * @param int $serverId ID du serveur
     * @param string $moduleName Nom du module
     * @param array $baseOptions Options de base
     * @return array|null Options enrichies ou null si échec
     */
    private function enrichConfigOptions($serverId, $moduleName, $baseOptions) {
        try {
            // Récupérer la configuration du serveur
            $query = "SELECT * FROM servers WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$serverId]);
            $server = $stmt->fetch();

            if (!$server) {
                return null;
            }

            // Charger le module
            $moduleInstance = $this->moduleManager->loadModule('servers', $moduleName);
            if (!$moduleInstance) {
                return null; // Retourner null au lieu d'exception (style WHMCS)
            }

            // Configurer le module
            $config = json_decode($server['config'], true) ?? [];
            if (method_exists($moduleInstance, 'initialize')) {
                $initResult = $moduleInstance->initialize($config);
                if (!$initResult) {
                    return null; // Retourner null au lieu d'exception (style WHMCS)
                }
            }

            // Récupérer les plans du serveur
            $plans = [];
            if (method_exists($moduleInstance, 'getAvailablePlans')) {
                $plans = $moduleInstance->getAvailablePlans();
            }

            // Enrichir les options avec les plans
            if (!empty($plans)) {
                $planOptions = [];
                foreach ($plans as $plan) {
                    $planOptions[$plan['id']] = $plan['id'] . ' - ' . $plan['name'];
                }
                $baseOptions['plan']['options'] = $planOptions;
            }

            // Ajouter d'autres options spécifiques au module
            if (method_exists($moduleInstance, 'getConfigurationFields')) {
                $moduleFields = $moduleInstance->getConfigurationFields();
                foreach ($moduleFields as $fieldName => $fieldConfig) {
                    $baseOptions[$fieldName] = $fieldConfig;
                }
            }

            return $baseOptions;

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de l'enrichissement des options", ['error' => $e->getMessage()]);
            return null; // Retourner null au lieu d'exception (style WHMCS)
        }
    }

    /**
     * Récupère les plans pour un module spécifique (style WHMCS)
     * 
     * @return array Réponse API
     */
    public function getModulePlans() {
        $serverId = $_GET['server_id'] ?? null;
        $moduleName = $_GET['module_name'] ?? '';

        if (!$serverId || !$moduleName) {
            return $this->sendError('ID serveur et nom de module requis');
        }

        try {
            // Récupérer la configuration du serveur
            $query = "SELECT * FROM servers WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$serverId]);
            $server = $stmt->fetch();

            if (!$server) {
                return $this->sendResponse([
                    'success' => true,
                    'message' => 'Serveur non trouvé',
                    'data' => []
                ], 200);
            }

            // Charger le module
            $moduleInstance = $this->moduleManager->loadModule('servers', $moduleName);
            if (!$moduleInstance) {
                return $this->sendResponse([
                    'success' => true,
                    'message' => 'Module non trouvé',
                    'data' => []
                ], 200);
            }

            // Configurer et récupérer les plans
            $config = json_decode($server['config'], true) ?? [];
            if (method_exists($moduleInstance, 'initialize')) {
                $moduleInstance->initialize($config);
            }

            $plans = [];

            // Recherche de la méthode appropriée pour récupérer les plans/packages
            // Utilise la même logique que AdminModuleController pour la compatibilité
            if (method_exists($moduleInstance, 'listPackages')) {
                $plans = $moduleInstance->listPackages();
            } elseif (method_exists($moduleInstance, 'getAvailablePlans')) {
                $plans = $moduleInstance->getAvailablePlans();
            } elseif (method_exists($moduleInstance, 'getPlans')) {
                $plans = $moduleInstance->getPlans();
            } elseif (method_exists($moduleInstance, 'getPackages')) {
                $plans = $moduleInstance->getPackages();
            }

            return $this->sendResponse([
                'success' => true,
                'message' => 'Plans récupérés avec succès',
                'data' => $plans
            ], 200);

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors de la récupération des plans", ['error' => $e->getMessage()]);
            // Style WHMCS : retourner un tableau vide au lieu d'une erreur
            return $this->sendResponse([
                'success' => true,
                'message' => 'Erreur lors de la récupération des plans',
                'data' => []
            ], 200);
        }
    }

    /**
     * Test de connexion à un serveur (style WHMCS TestConnection)
     * 
     * @return array Réponse API
     */
    public function testConnection() {
        $serverId = $_POST['server_id'] ?? null;
        $moduleName = $_POST['module_name'] ?? '';

        if (!$serverId || !$moduleName) {
            return $this->sendError('ID serveur et nom de module requis');
        }

        try {
            // Récupérer la configuration du serveur
            $query = "SELECT * FROM servers WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$serverId]);
            $server = $stmt->fetch();

            if (!$server) {
                return $this->sendError('Serveur non trouvé');
            }

            // Charger le module
            $moduleInstance = $this->moduleManager->loadModule('servers', $moduleName);
            if (!$moduleInstance) {
                return $this->sendError('Module non trouvé');
            }

            // Tester la connexion
            $config = json_decode($server['config'], true) ?? [];
            if (method_exists($moduleInstance, 'testConnection')) {
                $result = $moduleInstance->testConnection($config);
                
                return $this->sendResponse([
                    'success' => $result['success'] ?? false,
                    'message' => $result['message'] ?? 'Test de connexion effectué',
                    'data' => $result
                ], 200);
            } else {
                return $this->sendError('Le module ne supporte pas le test de connexion');
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error("Erreur lors du test de connexion", ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors du test de connexion: ' . $e->getMessage());
        }
    }
}

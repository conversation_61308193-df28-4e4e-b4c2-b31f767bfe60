<?php
/**
 * Contrôleur de gestion des produits pour l'administration
 */

namespace TechCMS\Api\V1\Controllers\Admin\Product;

use TechCMS\Api\V1\Controllers\Admin\AdminBaseController;
use TechCMS\Api\V1\Models\Product;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\StatsRealTime;

class AdminProductController extends AdminBaseController {
    protected $productModel;

    public function __construct() {
        parent::__construct();
        $this->productModel = new Product();
    }

    /**
     * Liste tous les produits
     */
    public function index() {
        $products = $this->productModel->getAll();

        return $this->sendResponse([
            'products' => $products
        ]);
    }

    /**
     * Récupère un produit spécifique
     */
    public function show($id) {
        // Si $id est un objet Request, c'est une erreur
        if (is_object($id) && get_class($id) === 'TechCMS\Common\Core\Request') {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "L'ID du produit est requis",
                "Erreur de requête"
            );
            return $this->sendError('ID du produit requis', 400);
        }

        $product = $this->productModel->getById($id);

        if (!$product) {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Impossible de trouver le produit avec l'ID $id",
                "Produit introuvable"
            );
            return $this->sendError('Produit non trouvé', 404);
        }

        return $this->sendResponse([
            'product' => $product
        ]);
    }

    /**
     * Crée un nouveau produit
     */
    public function store() {
        // Récupérer directement les données JSON de la requête
        $jsonData = file_get_contents('php://input');
        $data = json_decode($jsonData, true);
        
        // Log pour debug
        Logger::channel('api')->debug('Données reçues', ['data' => $data]);
        
        // Restructuration des données si elles sont dans un format imbriqué
        if (isset($data['details'])) {
            // Fusionner les données de details dans l'objet principal
            foreach ($data['details'] as $key => $value) {
                $data[$key] = $value;
            }
        }
        
        // Assurer que provisioning_type a une valeur correcte
        if (!isset($data['provisioning_type']) || empty($data['provisioning_type'])) {
            // Si provisioning_type n'est pas défini, utiliser 'manual' par défaut ou déterminer à partir de auto_provision
            if (isset($data['auto_provision']) && $data['auto_provision']) {
                $data['provisioning_type'] = 'auto';
            } else {
                $data['provisioning_type'] = 'manual';
            }
        }
        
        // Assurer que server_type a une valeur correcte
        if (!isset($data['server_type']) || empty($data['server_type'])) {
            // Utiliser le module comme server_type si non défini
            if (isset($data['module']) && !empty($data['module'])) {
                $data['server_type'] = $data['module'];
            }
            
            // Si un serveur est spécifié, récupérer son type
            if (isset($data['server_id']) && is_numeric($data['server_id'])) {
                $serverModel = new \TechCMS\Api\V1\Models\Server();
                $server = $serverModel->getById($data['server_id']);
                if ($server) {
                    $data['server_type'] = $server['type'];
                }
            }
        }
        
        // Assurer que package_name a une valeur (même vide)
        if (!isset($data['package_name'])) {
            $data['package_name'] = '';
        }
        
        // Log pour debug après restructuration
        Logger::channel('api')->debug('Données restructurées', ['data' => $data]);
        
        // Traiter l'image si elle est présente (base64)
        if (isset($data['image_url']) && !empty($data['image_url'])) {
            $data['image_url'] = $this->handleProductImage($data['image_url']);
            Logger::channel('api')->debug('Image traitée', ['image_url' => $data['image_url']]);
        }

        // Validation des données
        if (empty($data['name'])) {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Le nom du produit est requis",
                "Validation échouée"
            );
            return $this->sendError('Le nom du produit est requis', 400);
        }

        // IMPORTANT: On n'utilise plus cette préparation des données
        // car on a déjà des données restructurées correctement
        
        // Sauvegarder les données déjà restructurées
        Logger::channel('api')->debug('Envoi des données au modèle sans modification supplémentaire');

        // Créer le produit en utilisant directement les données restructurées
        $productModel = new \TechCMS\Api\V1\Models\Product();
        
        // Log pour débogage final
        Logger::channel('api')->debug('Valeurs finales avant création', ['data' => $data]);
        
        // Utiliser directement l'objet $data pour la création du produit
        $product = $productModel->create($data);

        if ($product) {
            // Envoyer un toast de succès
            sendSuccessToast(
                "Le produit \"" . $data['name'] . "\" a été créé avec succès",
                "Produit ajouté"
            );
            
            // Publier l'événement temps réel pour la création de produit
            $statsRealTime = StatsRealTime::getInstance();
            $statsRealTime->publishProductUpdate($product, 'create');
            Logger::channel('app')->info('Événement temps réel envoyé pour nouveau produit', [
                'product_id' => $product['id'],
                'name' => $product['name']
            ]);
            
            return $this->sendResponse([
                'product' => $product,
                'message' => 'Produit créé avec succès'
            ], 201);
        } else {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Une erreur est survenue lors de la création du produit",
                "Erreur de création"
            );
            
            return $this->sendError('Erreur lors de la création du produit', 500);
        }
    }

    /**
     * Gestion d'une image de produit (upload et sauvegarde)
     */
    private function handleProductImage($imageData) {
        // Vérifier si l'image est une donnée base64
        if (empty($imageData) || !is_string($imageData) || strlen($imageData) < 100) {
            return $imageData; // Retourner la valeur telle quelle si ce n'est pas une image base64
        }
        
        // Vérifier si c'est un chemin existant plutôt qu'une image base64
        if (strpos($imageData, '/storage/product-images/') === 0 && file_exists($_SERVER['DOCUMENT_ROOT'] . $imageData)) {
            return $imageData; // Retourner le chemin existant
        }

        // C'est une image base64, la traiter
        if (preg_match('/^data:image\/([a-z]+);base64,/i', $imageData, $matches)) {
            $extension = $matches[1]; // jpg, png, etc
            $base64_data = preg_replace('/^data:image\/[a-z]+;base64,/i', '', $imageData);
            $binary_data = base64_decode($base64_data);

            // Générer un nom de fichier unique
            $filename = 'product_' . time() . '_' . substr(md5(rand()), 0, 10) . '.' . $extension;
            $upload_dir = $_SERVER['DOCUMENT_ROOT'] . '/storage/product-images/';
            $file_path = $upload_dir . $filename;
            
            // Créer le répertoire s'il n'existe pas
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Enregistrer le fichier
            if (file_put_contents($file_path, $binary_data)) {
                // Retourner le chemin relatif à stocker en base de données
                return '/storage/product-images/' . $filename;
            }
        }
        
        // En cas d'échec, retourner null
        return null;
    }
    
    /**
     * Met à jour un produit existant
     */
    public function update($id) {
        // Si $id est un objet Request, c'est une erreur
        if (is_object($id) && get_class($id) === 'TechCMS\Common\Core\Request') {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "L'ID du produit est requis",
                "Erreur de requête"
            );
            return $this->sendError('ID du produit requis', 400);
        }

        $jsonData = file_get_contents('php://input');
        $data = json_decode($jsonData, true);

        // Vérifier si le produit existe
        $product = $this->productModel->getById($id);

        if (!$product) {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Impossible de trouver le produit avec l'ID $id",
                "Produit introuvable"
            );
            return $this->sendError('Produit non trouvé', 404);
        }

        // Préparation des données
        $name = $data['name'] ?? $product['name'];
        $slug = $data['slug'] ?? $product['slug'];
        $description = $data['description'] ?? $product['description'];
        $price = $data['price'] ?? $product['price'];
        $setup_fee = $data['setup_fee'] ?? $product['setup_fee'];
        $recurring = $data['recurring'] ?? $product['recurring'];
        $billing_cycle = $data['billing_cycle'] ?? $product['billing_cycle'];
        $status = $data['status'] ?? $product['status'];
        $product_type = $data['productType'] ?? $product['product_type'];
        $group_id = $data['group_id'] ?? $product['group_id'];
        $features = isset($data['features']) ? $data['features'] : json_decode($product['features'], true);
        $options = isset($data['options']) ? $data['options'] : json_decode($product['options'], true);
        $updated_at = date('Y-m-d H:i:s');

        // Nouveaux champs ajoutés
        $short_description = $data['short_description'] ?? $product['short_description'];
        
        // Traitement spécial pour l'image
        $image_url = isset($data['image_url']) ? $data['image_url'] : $product['image_url'];
        if (isset($data['image_url']) && !empty($data['image_url'])) {
            $image_url = $this->handleProductImage($data['image_url']);
            Logger::channel('api')->debug('Image traitée lors de la mise à jour', ['image_url' => $image_url]);
        }
        
        $color = $data['color'] ?? $product['color'];
        $welcome_email_id = $data['welcome_email_id'] ?? $product['welcome_email_id'];
        $hidden = isset($data['hidden']) ? (int)$data['hidden'] : (int)$product['hidden'];
        $featured = isset($data['featured']) ? (int)$data['featured'] : (int)$product['featured']; 
        $stock_control = isset($data['stock_control']) ? (int)$data['stock_control'] : (int)$product['stock_control'];
        $stock_quantity = $data['stock_quantity'] ?? $product['stock_quantity'];
        
        // Log pour débogage des nouveaux champs
        Logger::channel('api')->debug('Nouveaux champs de produit', [
            'short_description' => $short_description,
            'image_url' => $image_url,
            'color' => $color,
            'welcome_email_id' => $welcome_email_id,
            'hidden' => $hidden,
            'featured' => $featured,
            'stock_control' => $stock_control,
            'stock_quantity' => $stock_quantity,
        ]);

        // Champs de provisionnement automatique
        $auto_provision = $product['auto_provision'] ?? 0;
        $provisioning_type = $product['provisioning_type'] ?? null;
        $server_type = $product['server_type'] ?? null;
        $package_name = $product['package_name'] ?? null;

        if (isset($data['autoSetupOnOrder'])) {
            $auto_provision = $data['autoSetupOnOrder'] ? 1 : 0;
        } elseif (isset($data['autoSetupOnPayment'])) {
            $auto_provision = $data['autoSetupOnPayment'] ? 1 : 0;
        } elseif (isset($data['autoSetupOnPendingOrder'])) {
            $auto_provision = $data['autoSetupOnPendingOrder'] ? 1 : 0;
        } elseif (isset($data['noAutoSetup'])) {
            $auto_provision = $data['noAutoSetup'] ? 0 : $auto_provision;
        }

        if (isset($data['module'])) {
            $provisioning_type = (!empty($data['module']) && $data['module'] !== 'custom') ? $data['module'] : null;
        }

        if (isset($data['serverGroup'])) {
            $server_type = !empty($data['serverGroup']) ? $data['serverGroup'] : null;
        }

        // Pour les plans cPanel
        if (isset($data['moduleSettings']) && isset($data['moduleSettings']['cpanelPlan'])) {
            $package_name = $data['moduleSettings']['cpanelPlan'];
        }

        // Mise à jour dans la base de données
        $updatedProduct = $this->productModel->update($id, [
            'name' => $name,
            'slug' => $slug,
            'description' => $description,
            'short_description' => $short_description,
            'image_url' => $image_url,
            'color' => $color,
            'welcome_email_id' => $welcome_email_id,
            'price' => $price,
            'setup_fee' => $setup_fee,
            'recurring' => $recurring,
            'billing_cycle' => $billing_cycle,
            'status' => $status,
            'product_type' => $product_type,
            'group_id' => $group_id,
            'hidden' => $hidden,
            'featured' => $featured,
            'stock_control' => $stock_control,
            'stock_quantity' => $stock_quantity,
            'features' => $features,
            'options' => $options,
            'auto_provision' => $auto_provision,
            'provisioning_type' => $provisioning_type,
            'server_type' => $server_type,
            'package_name' => $package_name,

            // Champs de pricing par cycle
            'price_monthly' => $data['price_monthly'] ?? $product['price_monthly'] ?? 0,
            'price_quarterly' => $data['price_quarterly'] ?? $product['price_quarterly'] ?? 0,
            'price_semiannually' => $data['price_semiannually'] ?? $product['price_semiannually'] ?? 0,
            'price_annually' => $data['price_annually'] ?? $product['price_annually'] ?? 0,
            'price_biennially' => $data['price_biennially'] ?? $product['price_biennially'] ?? 0,
            'price_triennially' => $data['price_triennially'] ?? $product['price_triennially'] ?? 0,

            // Setup fees par cycle
            'setup_fee_monthly' => $data['setup_fee_monthly'] ?? $product['setup_fee_monthly'] ?? 0,
            'setup_fee_quarterly' => $data['setup_fee_quarterly'] ?? $product['setup_fee_quarterly'] ?? 0,
            'setup_fee_semiannually' => $data['setup_fee_semiannually'] ?? $product['setup_fee_semiannually'] ?? 0,
            'setup_fee_annually' => $data['setup_fee_annually'] ?? $product['setup_fee_annually'] ?? 0,
            'setup_fee_biennially' => $data['setup_fee_biennially'] ?? $product['setup_fee_biennially'] ?? 0,
            'setup_fee_triennially' => $data['setup_fee_triennially'] ?? $product['setup_fee_triennially'] ?? 0,

            // Configuration des cycles multiples
            'multiple_cycles_enabled' => $data['multiple_cycles_enabled'] ?? $product['multiple_cycles_enabled'] ?? 0,
            'available_cycles' => $data['available_cycles'] ?? $product['available_cycles'] ?? 'monthly',
            'default_cycle' => $data['default_cycle'] ?? $product['default_cycle'] ?? 'monthly',

            // Configuration du module et provisionnement
            'auto_provision' => $data['auto_provision'] ?? $product['auto_provision'] ?? 0,
            'provisioning_type' => $data['provisioning_type'] ?? $product['provisioning_type'] ?? 'manual',
            'server_type' => $data['server_type'] ?? $product['server_type'] ?? '',
            'server_id' => $data['server_id'] ?? $product['server_id'] ?? null,

            'updated_at' => $updated_at
        ]);

        if ($updatedProduct) {
            // Envoyer un toast de succès
            sendSuccessToast(
                "Le produit \"$name\" a été mis à jour avec succès",
                "Produit mis à jour"
            );
            
            // Publier l'événement temps réel pour la mise à jour de produit
            $statsRealTime = StatsRealTime::getInstance();
            $statsRealTime->publishProductUpdate($updatedProduct, 'update');
            Logger::channel('app')->info('Événement temps réel envoyé pour mise à jour produit', [
                'product_id' => $id,
                'name' => $name
            ]);
            
            return $this->sendResponse([
                'product' => $updatedProduct,
                'message' => 'Produit mis à jour avec succès'
            ]);
        } else {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Une erreur est survenue lors de la mise à jour du produit",
                "Erreur de mise à jour"
            );
            
            return $this->sendError('Erreur lors de la mise à jour du produit', 500);
        }
    }

    /**
     * Supprime un produit
     */
    public function destroy($id) {
        // Si $id est un objet Request, c'est une erreur
        if (is_object($id) && get_class($id) === 'TechCMS\Common\Core\Request') {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "L'ID du produit est requis",
                "Erreur de requête"
            );
            return $this->sendError('ID du produit requis', 400);
        }

        // Vérifier si le produit existe
        $product = $this->productModel->getById($id);

        if (!$product) {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Impossible de trouver le produit avec l'ID $id",
                "Produit introuvable"
            );
            return $this->sendError('Produit non trouvé', 404);
        }
        
        // Conserver le nom du produit et le chemin de l'image pour le traitement
        $productName = $product['name'];
        $productImage = $product['image_url'] ?? null;
        
        // Vérifier si le produit est utilisé par des services
        try {
            $db = \TechCMS\Common\Core\Database::getInstance();
            $query = "SELECT COUNT(*) FROM services WHERE product_id = :product_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':product_id', $id);
            $stmt->execute();
            $servicesCount = (int)$stmt->fetchColumn();
            
            if ($servicesCount > 0) {
                // Envoyer un toast d'erreur si le produit est utilisé par des services
                Logger::channel('api')->warning("Tentative de suppression d'un produit utilisé par des services", [
                    'product_id' => $id,
                    'product_name' => $productName,
                    'services_count' => $servicesCount
                ]);
                
                sendErrorToast(
                    "Le produit \"$productName\" est utilisé par $servicesCount service(s). Veuillez d'abord changer le produit de ces services ou les supprimer.",
                    "Suppression impossible"
                );
                return $this->sendError('Produit utilisé par des services', 400);
            }
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la vérification des services utilisant le produit', [
                'product_id' => $id, 
                'error' => $e->getMessage()
            ]);
        }
        
        Logger::channel('api')->info('Suppression du produit avec image', [
            'product_id' => $id,
            'product_name' => $productName,
            'image_url' => $productImage
        ]);

        // Suppression physique du produit
        $deleted = $this->productModel->delete($id);

        if ($deleted) {
            // Si le produit avait une image, la supprimer du serveur
            if (!empty($productImage) && strpos($productImage, '/storage/product-images/') === 0) {
                $physicalPath = $_SERVER['DOCUMENT_ROOT'] . $productImage;
                if (file_exists($physicalPath)) {
                    $imageDeleted = unlink($physicalPath);
                    Logger::channel('api')->info('Suppression de l\'image du produit', [
                        'chemin' => $physicalPath,
                        'succes' => $imageDeleted ? 'oui' : 'non'
                    ]);
                } else {
                    Logger::channel('api')->warning('Image produit introuvable lors de la suppression', [
                        'chemin' => $physicalPath
                    ]);
                }
            }
            
            // Préparer les données minimales du produit pour l'événement temps réel
            $productData = [
                'id' => $id,
                'name' => $productName
            ];
            
            // Publier l'événement temps réel pour la suppression du produit
            $statsRealTime = StatsRealTime::getInstance();
            $statsRealTime->publishProductUpdate($productData, 'delete');
            Logger::channel('app')->info('Événement temps réel envoyé pour suppression produit', [
                'product_id' => $id,
                'name' => $productName
            ]);
            
            // Envoyer un toast de succès
            sendSuccessToast(
                "Le produit \"$productName\" a été supprimé avec succès",
                "Produit supprimé"
            );
            
            return $this->sendResponse([
                'message' => 'Produit supprimé avec succès'
            ]);
        } else {
            // Envoyer un toast d'erreur
            sendErrorToast(
                "Une erreur est survenue lors de la suppression du produit",
                "Erreur de suppression"
            );
            
            return $this->sendError('Erreur lors de la suppression du produit', 500);
        }
    }

    /**
     * Génère un slug à partir d'un nom
     */
    private function generateSlug($name) {
        $slug = strtolower($name);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug;
    }
}

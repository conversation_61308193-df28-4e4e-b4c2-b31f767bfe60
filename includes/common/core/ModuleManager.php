<?php
/**
 * Gestionnaire de modules centralisé
 *
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Common\Core;

use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\Database;

// Définition de la constante ROOTDIR si elle n'est pas déjà définie
if (!defined('ROOTDIR')) {
    define('ROOTDIR', realpath(__DIR__ . '/../../'));
}

class ModuleManager {
    private static $instance = null;
    private $loadedModules = [];
    private $moduleCache = null;
    
    /**
     * Constructeur privé (pattern Singleton)
     */
    private function __construct() {
        // Initialisation - Utilisation de la base de données, plus besoin de chemin de fichier
        $this->loadModuleCache();
    }
    
    /**
     * Récupère l'instance unique du gestionnaire
     * 
     * @return ModuleManager
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Charge un module spécifique.
     * Si des paramètres sont fournis, une nouvelle instance non mise en cache est créée et initialisée.
     * Sinon, une instance générique mise en cache est retournée.
     * 
     * @param string $type Type de module (servers, gateways, etc.)
     * @param string $name Nom du module
     * @param array|null $params Paramètres de configuration pour une initialisation spécifique.
     * @return mixed Instance du module ou false en cas d'erreur
     */
    public function loadModule(string $type, string $name, array $params = null) {
        $key = $type . '_' . $name;

        // Si des paramètres sont fournis, nous créons une instance spécifique et non mise en cache.
        // Cela est crucial pour les modules qui nécessitent une configuration par serveur (ex: identifiants API).
        if ($params !== null) {
            $rootDir = defined('ROOTDIR') ? ROOTDIR : (defined('ROOTPATH') ? ROOTPATH : realpath(__DIR__ . '/../../..'));
            $modulePath = $rootDir . '/modules/' . $type . '/' . $name . '/module.php';
            if (!file_exists($modulePath)) {
                Logger::channel('app')->error("Fichier module.php manquant pour: {$type}/{$name}", [
                    'modulePath' => $modulePath,
                    'rootDir' => $rootDir,
                    'ROOTDIR_defined' => defined('ROOTDIR'),
                    'ROOTPATH_defined' => defined('ROOTPATH')
                ]);
                return false;
            }
            // Utiliser require_once pour éviter les redéfinitions de classe
            require_once $modulePath;

            $className = '\\TechCMS\\Modules\\' . ucfirst($type) . '\\' . ucfirst($name) . '\\Module';
            if (!class_exists($className)) {
                Logger::channel('app')->error("Classe de module non trouvée: {$className}");
                return false;
            }

            $moduleInstance = new $className();
            // Initialiser le module avec ses paramètres spécifiques
            if (method_exists($moduleInstance, 'initialize')) {
                $moduleInstance->initialize($params);
            }
            // Retourne l'instance fraîchement configurée mais ne la met PAS en cache
            return $moduleInstance; 
        }

        // Logique originale pour les instances génériques et mises en cache (sans configuration spécifique)
        if (isset($this->loadedModules[$key])) {
            return $this->loadedModules[$key];
        }

        $rootDir = defined('ROOTDIR') ? ROOTDIR : (defined('ROOTPATH') ? ROOTPATH : realpath(__DIR__ . '/../../..'));
        $modulePath = $rootDir . '/modules/' . $type . '/' . $name . '/module.php';
        if (!file_exists($modulePath)) {
            Logger::channel('app')->error("Fichier module.php manquant pour: {$type}/{$name}", [
                'modulePath' => $modulePath,
                'rootDir' => $rootDir,
                'ROOTDIR_defined' => defined('ROOTDIR'),
                'ROOTPATH_defined' => defined('ROOTPATH')
            ]);
            return false;
        }
        require_once $modulePath;

        $className = '\\TechCMS\\Modules\\' . ucfirst($type) . '\\' . ucfirst($name) . '\\Module';
        if (!class_exists($className)) {
            Logger::channel('app')->error("Classe de module non trouvée: {$className}");
            return false;
        }

        $moduleInstance = new $className();
        // Stocker l'instance générique pour les appels futurs
        $this->loadedModules[$key] = $moduleInstance;

        return $moduleInstance;
    }
    
    /**
     * Récupère la liste des modules disponibles d'un type spécifique
     * 
     * @param string $type Type de module
     * @return array Liste des modules disponibles
     */
    public function getAvailableModules(string $type) {
        $modulesDir = ROOTDIR . '/modules/' . $type;
        $modules = [];
        
        if (!is_dir($modulesDir)) {
            return $modules;
        }
        
        $dirs = scandir($modulesDir);
        foreach ($dirs as $dir) {
            if ($dir === '.' || $dir === '..' || !is_dir($modulesDir . '/' . $dir)) {
                continue;
            }
            
            // Vérifier l'existence du fichier principal
            if (file_exists($modulesDir . '/' . $dir . '/module.php')) {
                // Charger temporairement pour récupérer les métadonnées
                $module = $this->loadModule($type, $dir);
                if ($module) {
                    $modules[$dir] = $module->getMetadata();
                }
            }
        }
        
        return $modules;
    }
    
    /**
     * Appelle une fonction spécifique du module
     * 
     * @param string $type Type de module
     * @param string $name Nom du module
     * @param string $function Fonction à appeler
     * @param array $params Paramètres à passer à la fonction
     * @return mixed Résultat de la fonction ou false en cas d'erreur
     */
    public function callModuleFunction(string $type, string $name, string $function, array $params = []) {
        $module = $this->loadModule($type, $name);
        
        if (!$module) {
            return false;
        }
        
        if (!method_exists($module, $function)) {
            Logger::channel('app')->warning("Fonction {$function} non trouvée dans le module {$type}/{$name}");
            return false;
        }
        
        try {
            return call_user_func_array([$module, $function], $params);
        } catch (\Exception $e) {
            Logger::channel('app')->error("Erreur lors de l'appel de {$function} dans {$type}/{$name}", ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Scanne les dossiers des modules pour détecter tous les modules disponibles
     * 
     * @param bool $forceRescan Forcer une nouvelle détection même si le cache existe
     * @return array Catalogue des modules disponibles
     */
    public function scanForModules(bool $forceRescan = false) {
        $catalogue = [];
        
        // Vérifier si un cache existe et est valide en mémoire
        if (!$forceRescan && $this->moduleCache !== null) {
            return $this->moduleCache;
        }
        
        // Récupérer les modules depuis la base de données si pas de rescan forcé
        if (!$forceRescan) {
            $modules = $this->getModulesFromDatabase();
            if ($modules) {
                $this->moduleCache = $modules;
                return $modules;
            }
        }
        
        // Parcourir tous les types de modules
        $moduleTypes = ['servers', 'gateways', 'addons', 'fraud', 'registrars', 'reports', 'widgets'];
        
        foreach ($moduleTypes as $type) {
            $typeDir = ROOTDIR . '/modules/' . $type;
            
            if (!is_dir($typeDir)) {
                continue;
            }
            
            $catalogue[$type] = [];
            $moduleDirs = scandir($typeDir);
            
            foreach ($moduleDirs as $dir) {
                // Ignorer . et .. et les fichiers
                if ($dir === '.' || $dir === '..' || !is_dir($typeDir . '/' . $dir)) {
                    continue;
                }
                
                // Vérifier l'existence du fichier principal
                $modulePath = $typeDir . '/' . $dir;
                $moduleFile = $modulePath . '/module.php';
                
                if (!file_exists($moduleFile)) {
                    continue;
                }
                
                // Extraire les métadonnées sans charger complètement le module
                $metadata = $this->extractModuleMetadata($type, $dir);
                
                if ($metadata) {
                    $catalogue[$type][$dir] = $metadata;
                    
                    // Vérifier les dépendances
                    $catalogue[$type][$dir]['canBeEnabled'] = $this->checkModuleDependencies($metadata);
                }
            }
        }
        
        // Mettre en cache le catalogue et sauvegarder en base de données
        $this->moduleCache = $catalogue;
        $this->saveModulesToDatabase($catalogue);
        
        return $catalogue;
    }
    
    /**
     * Extrait les métadonnées d'un module
     * 
     * @param string $type Type de module
     * @param string $name Nom du module
     * @return array|false Métadonnées du module ou false en cas d'erreur
     */
    private function extractModuleMetadata(string $type, string $name) {
        try {
            // Essayer de charger temporairement le module pour récupérer les métadonnées
            $module = $this->loadModule($type, $name);
            
            if (!$module) {
                return false;
            }
            
            if (!method_exists($module, 'getMetadata')) {
                return [
                    'name' => $name, // Nom technique (clé)
                    'display_name' => ucfirst($name), // Nom d'affichage
                    'type' => $type,
                    'version' => '1.0.0',
                    'description' => 'No description available',
                    'author' => 'Unknown',
                    'features' => []
                ];
            }
            
            $metadata = $module->getMetadata();
            // Ne pas écraser le type si déjà défini par le module
            if (!isset($metadata['type'])) {
                $metadata['type'] = $type;
            }
            // Ajouter toujours la catégorie du module pour référence
            $metadata['category'] = $type;
            
            return $metadata;
        } catch (\Exception $e) {
            Logger::channel('app')->error("Erreur lors de l'extraction des métadonnées pour {$type}/{$name}", ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Vérifie les dépendances d'un module
     * 
     * @param array $metadata Métadonnées du module
     * @return bool True si toutes les dépendances sont satisfaites
     */
    private function checkModuleDependencies(array $metadata) {
        // Si pas de dépendances définies, le module peut être activé
        if (!isset($metadata['dependencies']) || empty($metadata['dependencies'])) {
            return true;
        }
        
        foreach ($metadata['dependencies'] as $dependency) {
            // Vérifier si la dépendance est une extension PHP
            if (isset($dependency['type']) && $dependency['type'] === 'php_extension') {
                if (!extension_loaded($dependency['name'])) {
                    return false;
                }
                continue;
            }
            
            // Vérifier si la dépendance est un autre module
            if (isset($dependency['type']) && $dependency['type'] === 'module') {
                $depType = $dependency['module_type'] ?? 'addons';
                $depName = $dependency['name'];
                $depModule = $this->loadModule($depType, $depName);
                
                if (!$depModule) {
                    return false;
                }
                
                // Vérifier la version si spécifiée
                if (isset($dependency['version'])) {
                    $depMetadata = $depModule->getMetadata();
                    $depVersion = $depMetadata['version'] ?? '0.0.0';
                    
                    if (!$this->checkVersionRequirement($depVersion, $dependency['version'])) {
                        return false;
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * Vérifie si une version satisfait une exigence
     * 
     * @param string $version Version actuelle
     * @param string $requirement Exigence de version (ex: ">=1.0.0")
     * @return bool True si la version satisfait l'exigence
     */
    private function checkVersionRequirement(string $version, string $requirement) {
        // Implémentation simple, à améliorer pour gérer des comparaisons plus complexes
        $operator = substr($requirement, 0, 2);
        if (in_array($operator, ['>=', '<=', '==', '!='])) {
            $reqVersion = substr($requirement, 2);
        } else {
            $operator = substr($requirement, 0, 1);
            if (in_array($operator, ['>', '<', '='])) {
                $reqVersion = substr($requirement, 1);
            } else {
                $operator = '==';
                $reqVersion = $requirement;
            }
        }
        
        return version_compare($version, $reqVersion, $operator);
    }
    
    /**
     * Charge le cache des modules depuis la base de données
     * 
     * @return bool Succès ou échec du chargement
     */
    private function loadModuleCache() {
        $modules = $this->getModulesFromDatabase();
        if ($modules) {
            $this->moduleCache = $modules;
            return true;
        }
        return false;
    }
    
    /**
     * Récupère les informations des modules depuis la base de données
     * 
     * @return array|null Catalogue des modules ou null en cas d'erreur
     */
    private function getModulesFromDatabase() {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT * FROM modules");
            $stmt->execute();
            
            $modules = [];
            
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $category = $row['category'];
                $name = $row['name'];
                
                if (!isset($modules[$category])) {
                    $modules[$category] = [];
                }
                
                $modules[$category][$name] = [
                    'name' => $name, // Le vrai nom technique du module (clé)
                    'display_name' => $row['display_name'], // Le nom d'affichage du module
                    'version' => $row['version'],
                    'author' => $row['author'],
                    'description' => $row['description'],
                    'category' => $row['category'],
                    'active' => (bool)$row['active'],
                    'features' => $row['features'] ? json_decode($row['features'], true) : []
                ];
            }
            
            return count($modules) > 0 ? $modules : null;
        } catch (\Exception $e) {
            Logger::channel('app')->error('Erreur lors de la récupération des modules depuis la base de données', ['error' => $e->getMessage()]);
            return null;
        }
    }
    
    /**
     * Sauvegarde les informations des modules dans la base de données
     * 
     * @param array $catalogue Catalogue des modules à enregistrer
     * @return bool Succès ou échec de l'opération
     */
    private function saveModulesToDatabase($catalogue) {
        try {
            $db = Database::getInstance();
            
            // Commencer une transaction
            $db->beginTransaction();
            
            // Vider la table pour une réinitialisation complète
            $db->query("TRUNCATE TABLE modules");
            
            // Préparer l'instruction d'insertion
            $stmt = $db->prepare("
                INSERT INTO modules (category, name, display_name, version, author, description, features, active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            // Parcourir le catalogue et insérer chaque module
            foreach ($catalogue as $category => $modules) {
                foreach ($modules as $name => $metadata) {
                    $features = isset($metadata['features']) ? json_encode($metadata['features']) : null;
                    $active = isset($metadata['active']) ? (int)$metadata['active'] : 0;
                    $displayName = isset($metadata['display_name']) ? $metadata['display_name'] : (isset($metadata['name']) ? $metadata['name'] : ucfirst($name));
                    
                    $stmt->execute([
                        $category,
                        $name,
                        $displayName,
                        $metadata['version'] ?? '1.0.0',
                        $metadata['author'] ?? 'Unknown',
                        $metadata['description'] ?? '',
                        $features,
                        $active
                    ]);
                }
            }
            
            // Valider la transaction
            $db->commit();
            return true;
        } catch (\Exception $e) {
            // En cas d'erreur, essayer d'annuler la transaction
            try {
                $db->rollBack();
            } catch (\Exception $rollbackEx) {
                // Ignorer l'erreur si pas de transaction active
            }
            
            Logger::channel('app')->error('Erreur lors de la sauvegarde des modules dans la base de données', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Active ou désactive un module
     * 
     * @param string $type Type de module
     * @param string $name Nom du module
     * @param bool $active État d'activation
     * @return bool Succès ou échec
     */
    public function setModuleActivation($type, $name, $active) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("UPDATE modules SET active = ? WHERE category = ? AND name = ?");
            $result = $stmt->execute([(int)$active, $type, $name]);
            
            if ($result) {
                // Rafraîchir le cache en mémoire
                $this->moduleCache = null;
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Logger::channel('app')->error('Erreur lors de la modification de l\'\u00e9tat du module', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Récupère la liste des modules actifs
     * 
     * @return array Liste des modules actifs par type
     */
    public function getActiveModules() {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT category, name FROM modules WHERE active = 1");
            $stmt->execute();
            
            $activeModules = [];
            
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $category = $row['category'];
                $name = $row['name'];
                
                if (!isset($activeModules[$category])) {
                    $activeModules[$category] = [];
                }
                
                $activeModules[$category][$name] = true;
            }
            
            return $activeModules;
        } catch (\Exception $e) {
            Logger::channel('app')->error('Erreur lors de la récupération des modules actifs', ['error' => $e->getMessage()]);
            return [];
        }
    }
}
